{"__meta": {"id": "01JXR5S1J5NS5Y4W23KKTVB3T9", "datetime": "2025-06-14 21:33:21", "utime": **********.350844, "method": "POST", "uri": "/admin/states/create", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749936800.476533, "end": **********.350862, "duration": 0.8743290901184082, "duration_str": "874ms", "measures": [{"label": "Booting", "start": 1749936800.476533, "relative_start": 0, "end": **********.107436, "relative_end": **********.107436, "duration": 0.****************, "duration_str": "631ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.107457, "relative_start": 0.****************, "end": **********.350865, "relative_end": 2.86102294921875e-06, "duration": 0.****************, "duration_str": "243ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.125553, "relative_start": 0.****************, "end": **********.141525, "relative_end": **********.141525, "duration": 0.015972137451171875, "duration_str": "15.97ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.346424, "relative_start": 0.****************, "end": **********.347984, "relative_end": **********.347984, "duration": 0.0015599727630615234, "duration_str": "1.56ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "10.x", "tooltip": {"Laravel Version": "10.48.29", "PHP Version": "8.3.17", "Environment": "local", "Debug Mode": "Enabled", "URL": "xmetr.gc", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "queries": {"count": 23, "nb_statements": 23, "nb_visible_statements": 23, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.07081000000000001, "accumulated_duration_str": "70.81ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 76}], "start": **********.148561, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 0, "width_percent": 0.607}, {"sql": "select count(*) as aggregate from `states` where `slug` = 'state-1'", "type": "query", "params": [], "bindings": ["state-1"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Concerns/ValidatesAttributes.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 948}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 660}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 459}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 494}], "start": **********.174532, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "DatabasePresenceVerifier.php:54", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php", "line": 54}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FValidation%2FDatabasePresenceVerifier.php&line=54", "ajax": false, "filename": "DatabasePresenceVerifier.php", "line": "54"}, "connection": "xmetr", "explain": null, "start_percent": 0.607, "width_percent": 0.706}, {"sql": "select `name`, `id` from `countries`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "platform/plugins/location/src/Forms/StateForm.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\location\\src\\Forms\\StateForm.php", "line": 24}, {"index": 15, "namespace": null, "name": "platform/core/base/src/Forms/FormAbstract.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Forms\\FormAbstract.php", "line": 98}, {"index": 16, "namespace": null, "name": "platform/packages/form-builder/src/FormBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\form-builder\\src\\FormBuilder.php", "line": 44}, {"index": 17, "namespace": null, "name": "platform/core/base/src/Forms/FormBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Forms\\FormBuilder.php", "line": 11}, {"index": 18, "namespace": null, "name": "platform/core/base/src/Forms/FormAbstract.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Forms\\FormAbstract.php", "line": 570}], "start": **********.177433, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "StateForm.php:24", "source": {"index": 14, "namespace": null, "name": "platform/plugins/location/src/Forms/StateForm.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\location\\src\\Forms\\StateForm.php", "line": 24}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Flocation%2Fsrc%2FForms%2FStateForm.php&line=24", "ajax": false, "filename": "StateForm.php", "line": "24"}, "connection": "xmetr", "explain": null, "start_percent": 1.313, "width_percent": 0.551}, {"sql": "select exists(select * from `states` where `slug` = 'state-1') as `exists`", "type": "query", "params": [], "bindings": ["state-1"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/core/base/src/Models/Concerns/HasSlug.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\Concerns\\HasSlug.php", "line": 21}, {"index": 12, "namespace": null, "name": "platform/plugins/location/src/Models/State.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\location\\src\\Models\\State.php", "line": 45}, {"index": 19, "namespace": null, "name": "platform/core/base/src/Forms/FormAbstract.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Forms\\FormAbstract.php", "line": 468}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Forms/FormAbstract.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Forms\\FormAbstract.php", "line": 464}, {"index": 23, "namespace": null, "name": "platform/plugins/location/src/Http/Controllers/StateController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\location\\src\\Http\\Controllers\\StateController.php", "line": 42}], "start": **********.20474, "duration": 0.00127, "duration_str": "1.27ms", "memory": 0, "memory_str": null, "filename": "HasSlug.php:21", "source": {"index": 11, "namespace": null, "name": "platform/core/base/src/Models/Concerns/HasSlug.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\Concerns\\HasSlug.php", "line": 21}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FConcerns%2FHasSlug.php&line=21", "ajax": false, "filename": "HasSlug.php", "line": "21"}, "connection": "xmetr", "explain": null, "start_percent": 1.864, "width_percent": 1.794}, {"sql": "insert into `states` (`status`, `name`, `slug`, `abbreviation`, `country_id`, `order`, `is_default`, `image`, `updated_at`, `created_at`) values ('published', 'State 1', 'state-1', 'S1', '9', '0', '0', null, '2025-06-14 21:33:21', '2025-06-14 21:33:21')", "type": "query", "params": [], "bindings": [{"value": "published", "label": "Published"}, "State 1", "state-1", "S1", "9", "0", "0", null, "2025-06-14 21:33:21", "2025-06-14 21:33:21"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Forms/FormAbstract.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Forms\\FormAbstract.php", "line": 468}, {"index": 18, "namespace": null, "name": "platform/core/base/src/Forms/FormAbstract.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Forms\\FormAbstract.php", "line": 464}, {"index": 19, "namespace": null, "name": "platform/plugins/location/src/Http/Controllers/StateController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\location\\src\\Http\\Controllers\\StateController.php", "line": 42}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.207556, "duration": 0.00594, "duration_str": "5.94ms", "memory": 0, "memory_str": null, "filename": "FormAbstract.php:468", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Forms/FormAbstract.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Forms\\FormAbstract.php", "line": 468}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FForms%2FFormAbstract.php&line=468", "ajax": false, "filename": "FormAbstract.php", "line": "468"}, "connection": "xmetr", "explain": null, "start_percent": 3.658, "width_percent": 8.389}, {"sql": "select `name`, `id` from `countries`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "platform/plugins/location/src/Forms/StateForm.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\location\\src\\Forms\\StateForm.php", "line": 24}, {"index": 15, "namespace": null, "name": "platform/core/base/src/Forms/FormAbstract.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Forms\\FormAbstract.php", "line": 98}, {"index": 16, "namespace": null, "name": "platform/packages/form-builder/src/Form.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\form-builder\\src\\Form.php", "line": 199}, {"index": 17, "namespace": null, "name": "platform/packages/form-builder/src/Form.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\form-builder\\src\\Form.php", "line": 405}, {"index": 18, "namespace": null, "name": "platform/core/base/src/Forms/FormAbstract.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Forms\\FormAbstract.php", "line": 470}], "start": **********.2149942, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "StateForm.php:24", "source": {"index": 14, "namespace": null, "name": "platform/plugins/location/src/Forms/StateForm.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\location\\src\\Forms\\StateForm.php", "line": 24}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Flocation%2Fsrc%2FForms%2FStateForm.php&line=24", "ajax": false, "filename": "StateForm.php", "line": "24"}, "connection": "xmetr", "explain": null, "start_percent": 12.046, "width_percent": 0.621}, {"sql": "select * from `meta_boxes` where (`meta_key` = 'seo_meta' and `reference_id` = 1 and `reference_type` = 'Xmetr\\\\Location\\\\Models\\\\State') limit 1", "type": "query", "params": [], "bindings": ["seo_meta", 1, "Xmetr\\Location\\Models\\State"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 18, "namespace": null, "name": "platform/core/base/src/Supports/MetaBox.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Supports\\MetaBox.php", "line": 147}, {"index": 20, "namespace": null, "name": "platform/packages/seo-helper/src/SeoHelper.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\seo-helper\\src\\SeoHelper.php", "line": 171}, {"index": 22, "namespace": null, "name": "platform/packages/seo-helper/src/Listeners/CreatedContentListener.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\seo-helper\\src\\Listeners\\CreatedContentListener.php", "line": 15}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 433}], "start": **********.230079, "duration": 0.0011, "duration_str": "1.1ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 12.668, "width_percent": 1.553}, {"sql": "select exists(select * from `slugs` where (`key` = 'state-1' and `prefix` = 'state') and `id` != 0) as `exists`", "type": "query", "params": [], "bindings": ["state-1", "state", 0], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/packages/slug/src/Services/SlugService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\slug\\src\\Services\\SlugService.php", "line": 42}, {"index": 12, "namespace": null, "name": "platform/packages/slug/src/Services/SlugService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\slug\\src\\Services\\SlugService.php", "line": 23}, {"index": 13, "namespace": null, "name": "platform/packages/slug/src/Listeners/CreatedContentListener.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\slug\\src\\Listeners\\CreatedContentListener.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 433}, {"index": 19, "namespace": null, "name": "platform/core/base/src/Forms/FormAbstract.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Forms\\FormAbstract.php", "line": 512}], "start": **********.2370121, "duration": 0.0008100000000000001, "duration_str": "810μs", "memory": 0, "memory_str": null, "filename": "SlugService.php:42", "source": {"index": 11, "namespace": null, "name": "platform/packages/slug/src/Services/SlugService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\slug\\src\\Services\\SlugService.php", "line": 42}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fpackages%2Fslug%2Fsrc%2FServices%2FSlugService.php&line=42", "ajax": false, "filename": "SlugService.php", "line": "42"}, "connection": "xmetr", "explain": null, "start_percent": 14.221, "width_percent": 1.144}, {"sql": "insert into `slugs` (`key`, `reference_type`, `reference_id`, `prefix`, `updated_at`, `created_at`) values ('state-1', 'Xmetr\\\\Location\\\\Models\\\\State', 1, 'state', '2025-06-14 21:33:21', '2025-06-14 21:33:21')", "type": "query", "params": [], "bindings": ["state-1", "Xmetr\\Location\\Models\\State", 1, "state", "2025-06-14 21:33:21", "2025-06-14 21:33:21"], "hints": null, "show_copy": true, "backtrace": [{"index": 18, "namespace": null, "name": "platform/packages/slug/src/Listeners/CreatedContentListener.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\slug\\src\\Listeners\\CreatedContentListener.php", "line": 42}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 433}, {"index": 24, "namespace": null, "name": "platform/core/base/src/Forms/FormAbstract.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Forms\\FormAbstract.php", "line": 512}, {"index": 25, "namespace": null, "name": "platform/core/base/src/Forms/FormAbstract.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Forms\\FormAbstract.php", "line": 505}, {"index": 26, "namespace": null, "name": "platform/core/base/src/Forms/FormAbstract.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Forms\\FormAbstract.php", "line": 464}], "start": **********.240755, "duration": 0.00418, "duration_str": "4.18ms", "memory": 0, "memory_str": null, "filename": "CreatedContentListener.php:42", "source": {"index": 18, "namespace": null, "name": "platform/packages/slug/src/Listeners/CreatedContentListener.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\slug\\src\\Listeners\\CreatedContentListener.php", "line": 42}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fpackages%2Fslug%2Fsrc%2FListeners%2FCreatedContentListener.php&line=42", "ajax": false, "filename": "CreatedContentListener.php", "line": "42"}, "connection": "xmetr", "explain": null, "start_percent": 15.365, "width_percent": 5.903}, {"sql": "select `lang_code`, `lang_is_default` from `languages` order by `lang_order` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 16, "namespace": null, "name": "platform/plugins/language/src/LanguageManager.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\language\\src\\LanguageManager.php", "line": 156}, {"index": 18, "namespace": null, "name": "platform/plugins/language-advanced/src/Listeners/AddDefaultTranslations.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\language-advanced\\src\\Listeners\\AddDefaultTranslations.php", "line": 20}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 433}, {"index": 24, "namespace": null, "name": "platform/core/base/src/Forms/FormAbstract.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Forms\\FormAbstract.php", "line": 512}], "start": **********.249918, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 21.268, "width_percent": 0.664}, {"sql": "select exists(select * from `states_translations` where (`lang_code` = 'ru_RU' and `states_id` = 1)) as `exists`", "type": "query", "params": [], "bindings": ["ru_RU", 1], "hints": null, "show_copy": true, "backtrace": [{"index": 10, "namespace": null, "name": "platform/plugins/language-advanced/src/Listeners/AddDefaultTranslations.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\language-advanced\\src\\Listeners\\AddDefaultTranslations.php", "line": 30}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 433}, {"index": 16, "namespace": null, "name": "platform/core/base/src/Forms/FormAbstract.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Forms\\FormAbstract.php", "line": 512}, {"index": 17, "namespace": null, "name": "platform/core/base/src/Forms/FormAbstract.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Forms\\FormAbstract.php", "line": 505}, {"index": 18, "namespace": null, "name": "platform/core/base/src/Forms/FormAbstract.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Forms\\FormAbstract.php", "line": 464}], "start": **********.2527409, "duration": 0.00183, "duration_str": "1.83ms", "memory": 0, "memory_str": null, "filename": "AddDefaultTranslations.php:30", "source": {"index": 10, "namespace": null, "name": "platform/plugins/language-advanced/src/Listeners/AddDefaultTranslations.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\language-advanced\\src\\Listeners\\AddDefaultTranslations.php", "line": 30}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Flanguage-advanced%2Fsrc%2FListeners%2FAddDefaultTranslations.php&line=30", "ajax": false, "filename": "AddDefaultTranslations.php", "line": "30"}, "connection": "xmetr", "explain": null, "start_percent": 21.932, "width_percent": 2.584}, {"sql": "select column_name as `name`, data_type as `type_name`, column_type as `type`, collation_name as `collation`, is_nullable as `nullable`, column_default as `default`, column_comment as `comment`, extra as `extra` from information_schema.columns where table_schema = 'xmetr' and table_name = 'states_translations' order by ordinal_position asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/plugins/language-advanced/src/Listeners/AddDefaultTranslations.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\language-advanced\\src\\Listeners\\AddDefaultTranslations.php", "line": 39}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 433}, {"index": 18, "namespace": null, "name": "platform/core/base/src/Forms/FormAbstract.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Forms\\FormAbstract.php", "line": 512}, {"index": 19, "namespace": null, "name": "platform/core/base/src/Forms/FormAbstract.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Forms\\FormAbstract.php", "line": 505}, {"index": 20, "namespace": null, "name": "platform/core/base/src/Forms/FormAbstract.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Forms\\FormAbstract.php", "line": 464}], "start": **********.255732, "duration": 0.01298, "duration_str": "12.98ms", "memory": 0, "memory_str": null, "filename": "AddDefaultTranslations.php:39", "source": {"index": 12, "namespace": null, "name": "platform/plugins/language-advanced/src/Listeners/AddDefaultTranslations.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\language-advanced\\src\\Listeners\\AddDefaultTranslations.php", "line": 39}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Flanguage-advanced%2Fsrc%2FListeners%2FAddDefaultTranslations.php&line=39", "ajax": false, "filename": "AddDefaultTranslations.php", "line": "39"}, "connection": "xmetr", "explain": null, "start_percent": 24.516, "width_percent": 18.331}, {"sql": "insert into `states_translations` (`name`, `slug`, `abbreviation`, `lang_code`, `states_id`) values ('State 1', '', 'S1', 'ru_RU', 1)", "type": "query", "params": [], "bindings": ["State 1", "", "S1", "ru_RU", 1], "hints": null, "show_copy": true, "backtrace": [{"index": 10, "namespace": null, "name": "platform/plugins/language-advanced/src/Listeners/AddDefaultTranslations.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\language-advanced\\src\\Listeners\\AddDefaultTranslations.php", "line": 53}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 433}, {"index": 16, "namespace": null, "name": "platform/core/base/src/Forms/FormAbstract.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Forms\\FormAbstract.php", "line": 512}, {"index": 17, "namespace": null, "name": "platform/core/base/src/Forms/FormAbstract.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Forms\\FormAbstract.php", "line": 505}, {"index": 18, "namespace": null, "name": "platform/core/base/src/Forms/FormAbstract.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Forms\\FormAbstract.php", "line": 464}], "start": **********.271527, "duration": 0.00391, "duration_str": "3.91ms", "memory": 0, "memory_str": null, "filename": "AddDefaultTranslations.php:53", "source": {"index": 10, "namespace": null, "name": "platform/plugins/language-advanced/src/Listeners/AddDefaultTranslations.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\language-advanced\\src\\Listeners\\AddDefaultTranslations.php", "line": 53}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Flanguage-advanced%2Fsrc%2FListeners%2FAddDefaultTranslations.php&line=53", "ajax": false, "filename": "AddDefaultTranslations.php", "line": "53"}, "connection": "xmetr", "explain": null, "start_percent": 42.847, "width_percent": 5.522}, {"sql": "select exists(select * from `states_translations` where (`lang_code` = 'es_CL' and `states_id` = 1)) as `exists`", "type": "query", "params": [], "bindings": ["es_CL", 1], "hints": null, "show_copy": true, "backtrace": [{"index": 10, "namespace": null, "name": "platform/plugins/language-advanced/src/Listeners/AddDefaultTranslations.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\language-advanced\\src\\Listeners\\AddDefaultTranslations.php", "line": 30}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 433}, {"index": 16, "namespace": null, "name": "platform/core/base/src/Forms/FormAbstract.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Forms\\FormAbstract.php", "line": 512}, {"index": 17, "namespace": null, "name": "platform/core/base/src/Forms/FormAbstract.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Forms\\FormAbstract.php", "line": 505}, {"index": 18, "namespace": null, "name": "platform/core/base/src/Forms/FormAbstract.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Forms\\FormAbstract.php", "line": 464}], "start": **********.277443, "duration": 0.00268, "duration_str": "2.68ms", "memory": 0, "memory_str": null, "filename": "AddDefaultTranslations.php:30", "source": {"index": 10, "namespace": null, "name": "platform/plugins/language-advanced/src/Listeners/AddDefaultTranslations.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\language-advanced\\src\\Listeners\\AddDefaultTranslations.php", "line": 30}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Flanguage-advanced%2Fsrc%2FListeners%2FAddDefaultTranslations.php&line=30", "ajax": false, "filename": "AddDefaultTranslations.php", "line": "30"}, "connection": "xmetr", "explain": null, "start_percent": 48.369, "width_percent": 3.785}, {"sql": "select column_name as `name`, data_type as `type_name`, column_type as `type`, collation_name as `collation`, is_nullable as `nullable`, column_default as `default`, column_comment as `comment`, extra as `extra` from information_schema.columns where table_schema = 'xmetr' and table_name = 'states_translations' order by ordinal_position asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/plugins/language-advanced/src/Listeners/AddDefaultTranslations.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\language-advanced\\src\\Listeners\\AddDefaultTranslations.php", "line": 39}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 433}, {"index": 18, "namespace": null, "name": "platform/core/base/src/Forms/FormAbstract.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Forms\\FormAbstract.php", "line": 512}, {"index": 19, "namespace": null, "name": "platform/core/base/src/Forms/FormAbstract.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Forms\\FormAbstract.php", "line": 505}, {"index": 20, "namespace": null, "name": "platform/core/base/src/Forms/FormAbstract.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Forms\\FormAbstract.php", "line": 464}], "start": **********.281533, "duration": 0.00454, "duration_str": "4.54ms", "memory": 0, "memory_str": null, "filename": "AddDefaultTranslations.php:39", "source": {"index": 12, "namespace": null, "name": "platform/plugins/language-advanced/src/Listeners/AddDefaultTranslations.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\language-advanced\\src\\Listeners\\AddDefaultTranslations.php", "line": 39}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Flanguage-advanced%2Fsrc%2FListeners%2FAddDefaultTranslations.php&line=39", "ajax": false, "filename": "AddDefaultTranslations.php", "line": "39"}, "connection": "xmetr", "explain": null, "start_percent": 52.154, "width_percent": 6.412}, {"sql": "insert into `states_translations` (`name`, `slug`, `abbreviation`, `lang_code`, `states_id`) values ('State 1', '', 'S1', 'es_CL', 1)", "type": "query", "params": [], "bindings": ["State 1", "", "S1", "es_CL", 1], "hints": null, "show_copy": true, "backtrace": [{"index": 10, "namespace": null, "name": "platform/plugins/language-advanced/src/Listeners/AddDefaultTranslations.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\language-advanced\\src\\Listeners\\AddDefaultTranslations.php", "line": 53}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 433}, {"index": 16, "namespace": null, "name": "platform/core/base/src/Forms/FormAbstract.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Forms\\FormAbstract.php", "line": 512}, {"index": 17, "namespace": null, "name": "platform/core/base/src/Forms/FormAbstract.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Forms\\FormAbstract.php", "line": 505}, {"index": 18, "namespace": null, "name": "platform/core/base/src/Forms/FormAbstract.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Forms\\FormAbstract.php", "line": 464}], "start": **********.2906, "duration": 0.00419, "duration_str": "4.19ms", "memory": 0, "memory_str": null, "filename": "AddDefaultTranslations.php:53", "source": {"index": 10, "namespace": null, "name": "platform/plugins/language-advanced/src/Listeners/AddDefaultTranslations.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\language-advanced\\src\\Listeners\\AddDefaultTranslations.php", "line": 53}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Flanguage-advanced%2Fsrc%2FListeners%2FAddDefaultTranslations.php&line=53", "ajax": false, "filename": "AddDefaultTranslations.php", "line": "53"}, "connection": "xmetr", "explain": null, "start_percent": 58.565, "width_percent": 5.917}, {"sql": "select exists(select * from `states_translations` where (`lang_code` = 'ar' and `states_id` = 1)) as `exists`", "type": "query", "params": [], "bindings": ["ar", 1], "hints": null, "show_copy": true, "backtrace": [{"index": 10, "namespace": null, "name": "platform/plugins/language-advanced/src/Listeners/AddDefaultTranslations.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\language-advanced\\src\\Listeners\\AddDefaultTranslations.php", "line": 30}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 433}, {"index": 16, "namespace": null, "name": "platform/core/base/src/Forms/FormAbstract.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Forms\\FormAbstract.php", "line": 512}, {"index": 17, "namespace": null, "name": "platform/core/base/src/Forms/FormAbstract.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Forms\\FormAbstract.php", "line": 505}, {"index": 18, "namespace": null, "name": "platform/core/base/src/Forms/FormAbstract.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Forms\\FormAbstract.php", "line": 464}], "start": **********.298791, "duration": 0.00074, "duration_str": "740μs", "memory": 0, "memory_str": null, "filename": "AddDefaultTranslations.php:30", "source": {"index": 10, "namespace": null, "name": "platform/plugins/language-advanced/src/Listeners/AddDefaultTranslations.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\language-advanced\\src\\Listeners\\AddDefaultTranslations.php", "line": 30}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Flanguage-advanced%2Fsrc%2FListeners%2FAddDefaultTranslations.php&line=30", "ajax": false, "filename": "AddDefaultTranslations.php", "line": "30"}, "connection": "xmetr", "explain": null, "start_percent": 64.482, "width_percent": 1.045}, {"sql": "select column_name as `name`, data_type as `type_name`, column_type as `type`, collation_name as `collation`, is_nullable as `nullable`, column_default as `default`, column_comment as `comment`, extra as `extra` from information_schema.columns where table_schema = 'xmetr' and table_name = 'states_translations' order by ordinal_position asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/plugins/language-advanced/src/Listeners/AddDefaultTranslations.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\language-advanced\\src\\Listeners\\AddDefaultTranslations.php", "line": 39}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 433}, {"index": 18, "namespace": null, "name": "platform/core/base/src/Forms/FormAbstract.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Forms\\FormAbstract.php", "line": 512}, {"index": 19, "namespace": null, "name": "platform/core/base/src/Forms/FormAbstract.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Forms\\FormAbstract.php", "line": 505}, {"index": 20, "namespace": null, "name": "platform/core/base/src/Forms/FormAbstract.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Forms\\FormAbstract.php", "line": 464}], "start": **********.3008661, "duration": 0.00434, "duration_str": "4.34ms", "memory": 0, "memory_str": null, "filename": "AddDefaultTranslations.php:39", "source": {"index": 12, "namespace": null, "name": "platform/plugins/language-advanced/src/Listeners/AddDefaultTranslations.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\language-advanced\\src\\Listeners\\AddDefaultTranslations.php", "line": 39}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Flanguage-advanced%2Fsrc%2FListeners%2FAddDefaultTranslations.php&line=39", "ajax": false, "filename": "AddDefaultTranslations.php", "line": "39"}, "connection": "xmetr", "explain": null, "start_percent": 65.527, "width_percent": 6.129}, {"sql": "insert into `states_translations` (`name`, `slug`, `abbreviation`, `lang_code`, `states_id`) values ('State 1', '', 'S1', 'ar', 1)", "type": "query", "params": [], "bindings": ["State 1", "", "S1", "ar", 1], "hints": null, "show_copy": true, "backtrace": [{"index": 10, "namespace": null, "name": "platform/plugins/language-advanced/src/Listeners/AddDefaultTranslations.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\language-advanced\\src\\Listeners\\AddDefaultTranslations.php", "line": 53}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 433}, {"index": 16, "namespace": null, "name": "platform/core/base/src/Forms/FormAbstract.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Forms\\FormAbstract.php", "line": 512}, {"index": 17, "namespace": null, "name": "platform/core/base/src/Forms/FormAbstract.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Forms\\FormAbstract.php", "line": 505}, {"index": 18, "namespace": null, "name": "platform/core/base/src/Forms/FormAbstract.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Forms\\FormAbstract.php", "line": 464}], "start": **********.30788, "duration": 0.0038599999999999997, "duration_str": "3.86ms", "memory": 0, "memory_str": null, "filename": "AddDefaultTranslations.php:53", "source": {"index": 10, "namespace": null, "name": "platform/plugins/language-advanced/src/Listeners/AddDefaultTranslations.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\language-advanced\\src\\Listeners\\AddDefaultTranslations.php", "line": 53}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Flanguage-advanced%2Fsrc%2FListeners%2FAddDefaultTranslations.php&line=53", "ajax": false, "filename": "AddDefaultTranslations.php", "line": "53"}, "connection": "xmetr", "explain": null, "start_percent": 71.657, "width_percent": 5.451}, {"sql": "select exists(select * from `states_translations` where (`lang_code` = 'ur' and `states_id` = 1)) as `exists`", "type": "query", "params": [], "bindings": ["ur", 1], "hints": null, "show_copy": true, "backtrace": [{"index": 10, "namespace": null, "name": "platform/plugins/language-advanced/src/Listeners/AddDefaultTranslations.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\language-advanced\\src\\Listeners\\AddDefaultTranslations.php", "line": 30}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 433}, {"index": 16, "namespace": null, "name": "platform/core/base/src/Forms/FormAbstract.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Forms\\FormAbstract.php", "line": 512}, {"index": 17, "namespace": null, "name": "platform/core/base/src/Forms/FormAbstract.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Forms\\FormAbstract.php", "line": 505}, {"index": 18, "namespace": null, "name": "platform/core/base/src/Forms/FormAbstract.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Forms\\FormAbstract.php", "line": 464}], "start": **********.3136861, "duration": 0.00103, "duration_str": "1.03ms", "memory": 0, "memory_str": null, "filename": "AddDefaultTranslations.php:30", "source": {"index": 10, "namespace": null, "name": "platform/plugins/language-advanced/src/Listeners/AddDefaultTranslations.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\language-advanced\\src\\Listeners\\AddDefaultTranslations.php", "line": 30}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Flanguage-advanced%2Fsrc%2FListeners%2FAddDefaultTranslations.php&line=30", "ajax": false, "filename": "AddDefaultTranslations.php", "line": "30"}, "connection": "xmetr", "explain": null, "start_percent": 77.108, "width_percent": 1.455}, {"sql": "select column_name as `name`, data_type as `type_name`, column_type as `type`, collation_name as `collation`, is_nullable as `nullable`, column_default as `default`, column_comment as `comment`, extra as `extra` from information_schema.columns where table_schema = 'xmetr' and table_name = 'states_translations' order by ordinal_position asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/plugins/language-advanced/src/Listeners/AddDefaultTranslations.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\language-advanced\\src\\Listeners\\AddDefaultTranslations.php", "line": 39}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 433}, {"index": 18, "namespace": null, "name": "platform/core/base/src/Forms/FormAbstract.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Forms\\FormAbstract.php", "line": 512}, {"index": 19, "namespace": null, "name": "platform/core/base/src/Forms/FormAbstract.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Forms\\FormAbstract.php", "line": 505}, {"index": 20, "namespace": null, "name": "platform/core/base/src/Forms/FormAbstract.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Forms\\FormAbstract.php", "line": 464}], "start": **********.316474, "duration": 0.00366, "duration_str": "3.66ms", "memory": 0, "memory_str": null, "filename": "AddDefaultTranslations.php:39", "source": {"index": 12, "namespace": null, "name": "platform/plugins/language-advanced/src/Listeners/AddDefaultTranslations.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\language-advanced\\src\\Listeners\\AddDefaultTranslations.php", "line": 39}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Flanguage-advanced%2Fsrc%2FListeners%2FAddDefaultTranslations.php&line=39", "ajax": false, "filename": "AddDefaultTranslations.php", "line": "39"}, "connection": "xmetr", "explain": null, "start_percent": 78.562, "width_percent": 5.169}, {"sql": "insert into `states_translations` (`name`, `slug`, `abbreviation`, `lang_code`, `states_id`) values ('State 1', '', 'S1', 'ur', 1)", "type": "query", "params": [], "bindings": ["State 1", "", "S1", "ur", 1], "hints": null, "show_copy": true, "backtrace": [{"index": 10, "namespace": null, "name": "platform/plugins/language-advanced/src/Listeners/AddDefaultTranslations.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\language-advanced\\src\\Listeners\\AddDefaultTranslations.php", "line": 53}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 433}, {"index": 16, "namespace": null, "name": "platform/core/base/src/Forms/FormAbstract.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Forms\\FormAbstract.php", "line": 512}, {"index": 17, "namespace": null, "name": "platform/core/base/src/Forms/FormAbstract.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Forms\\FormAbstract.php", "line": 505}, {"index": 18, "namespace": null, "name": "platform/core/base/src/Forms/FormAbstract.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Forms\\FormAbstract.php", "line": 464}], "start": **********.322792, "duration": 0.00684, "duration_str": "6.84ms", "memory": 0, "memory_str": null, "filename": "AddDefaultTranslations.php:53", "source": {"index": 10, "namespace": null, "name": "platform/plugins/language-advanced/src/Listeners/AddDefaultTranslations.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\language-advanced\\src\\Listeners\\AddDefaultTranslations.php", "line": 53}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Flanguage-advanced%2Fsrc%2FListeners%2FAddDefaultTranslations.php&line=53", "ajax": false, "filename": "AddDefaultTranslations.php", "line": "53"}, "connection": "xmetr", "explain": null, "start_percent": 83.731, "width_percent": 9.66}, {"sql": "insert into `audit_histories` (`user_agent`, `ip_address`, `module`, `action`, `user_id`, `reference_user`, `reference_id`, `reference_name`, `type`, `created_at`, `updated_at`, `request`) values ('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '127.0.0.1', 'state', 'created', 1, 1, 1, 'State 1', 'info', '2025-06-14 21:33:21', '2025-06-14 21:33:21', '{\\\"name\\\":\\\"State 1\\\",\\\"model\\\":\\\"Xmetr\\\\\\\\Location\\\\\\\\Models\\\\\\\\State\\\",\\\"slug\\\":\\\"state-1\\\",\\\"slug_id\\\":\\\"0\\\",\\\"is_slug_editable\\\":\\\"1\\\",\\\"abbreviation\\\":\\\"S1\\\",\\\"country_id\\\":\\\"9\\\",\\\"order\\\":\\\"0\\\",\\\"is_default\\\":\\\"0\\\",\\\"seo_meta\\\":{\\\"seo_title\\\":null,\\\"seo_description\\\":null,\\\"index\\\":\\\"index\\\"},\\\"seo_meta_image\\\":null,\\\"status\\\":\\\"published\\\",\\\"image\\\":null,\\\"submitter\\\":\\\"apply\\\"}')", "type": "query", "params": [], "bindings": ["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "127.0.0.1", "state", "created", 1, 1, 1, "State 1", "info", "2025-06-14 21:33:21", "2025-06-14 21:33:21", "{\"name\":\"State 1\",\"model\":\"Xmetr\\\\Location\\\\Models\\\\State\",\"slug\":\"state-1\",\"slug_id\":\"0\",\"is_slug_editable\":\"1\",\"abbreviation\":\"S1\",\"country_id\":\"9\",\"order\":\"0\",\"is_default\":\"0\",\"seo_meta\":{\"seo_title\":null,\"seo_description\":null,\"index\":\"index\"},\"seo_meta_image\":null,\"status\":\"published\",\"image\":null,\"submitter\":\"apply\"}"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/plugins/audit-log/src/Listeners/AuditHandlerListener.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\audit-log\\src\\Listeners\\AuditHandlerListener.php", "line": 60}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 433}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 433}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Forms/FormAbstract.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Forms\\FormAbstract.php", "line": 512}, {"index": 23, "namespace": null, "name": "platform/core/base/src/Forms/FormAbstract.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Forms\\FormAbstract.php", "line": 505}], "start": **********.336983, "duration": 0.00468, "duration_str": "4.68ms", "memory": 0, "memory_str": null, "filename": "AuditHandlerListener.php:60", "source": {"index": 11, "namespace": null, "name": "platform/plugins/audit-log/src/Listeners/AuditHandlerListener.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\audit-log\\src\\Listeners\\AuditHandlerListener.php", "line": 60}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Faudit-log%2Fsrc%2FListeners%2FAuditHandlerListener.php&line=60", "ajax": false, "filename": "AuditHandlerListener.php", "line": "60"}, "connection": "xmetr", "explain": null, "start_percent": 93.391, "width_percent": 6.609}]}, "models": {"data": {"Xmetr\\Location\\Models\\Country": {"value": 20, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Flocation%2Fsrc%2FModels%2FCountry.php&line=1", "ajax": false, "filename": "Country.php", "line": "?"}}, "Xmetr\\Language\\Models\\Language": {"value": 5, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Flanguage%2Fsrc%2FModels%2FLanguage.php&line=1", "ajax": false, "filename": "Language.php", "line": "?"}}, "Xmetr\\ACL\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Facl%2Fsrc%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Xmetr\\Base\\Models\\MetaBox": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FMetaBox.php&line=1", "ajax": false, "filename": "MetaBox.php", "line": "?"}}}, "count": 27, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "302 Found", "full_url": "https://xmetr.gc/admin/states/create", "action_name": "state.create.store", "controller_action": "Xmetr\\Location\\Http\\Controllers\\StateController@store", "uri": "POST admin/states/create", "controller": "Xmetr\\Location\\Http\\Controllers\\StateController@store<a href=\"phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Flocation%2Fsrc%2FHttp%2FControllers%2FStateController.php&line=39\" class=\"phpdebugbar-widgets-editor-link\"></a>", "namespace": "Xmetr\\Location\\Http\\Controllers", "prefix": "admin/states", "file": "<a href=\"phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Flocation%2Fsrc%2FHttp%2FControllers%2FStateController.php&line=39\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">platform/plugins/location/src/Http/Controllers/StateController.php:39-49</a>", "middleware": "web, core, auth", "duration": "875ms", "peak_memory": "46MB", "response": "Redirect to https://xmetr.gc/admin/states/edit/1", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1408958680 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1408958680\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-619391592 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Rjk5dM1E37zSzatTdbPstutuCWngDisDc2aBepFu</span>\"\n  \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"7 characters\">State 1</span>\"\n  \"<span class=sf-dump-key>model</span>\" => \"<span class=sf-dump-str title=\"27 characters\">Xmetr\\Location\\Models\\State</span>\"\n  \"<span class=sf-dump-key>slug</span>\" => \"<span class=sf-dump-str title=\"7 characters\">state-1</span>\"\n  \"<span class=sf-dump-key>slug_id</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>is_slug_editable</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>abbreviation</span>\" => \"<span class=sf-dump-str title=\"2 characters\">S1</span>\"\n  \"<span class=sf-dump-key>country_id</span>\" => \"<span class=sf-dump-str>9</span>\"\n  \"<span class=sf-dump-key>order</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>is_default</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>seo_meta</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>seo_title</span>\" => <span class=sf-dump-const>null</span>\n    \"<span class=sf-dump-key>seo_description</span>\" => <span class=sf-dump-const>null</span>\n    \"<span class=sf-dump-key>index</span>\" => \"<span class=sf-dump-str title=\"5 characters\">index</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>seo_meta_image</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str title=\"9 characters\">published</span>\"\n  \"<span class=sf-dump-key>image</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>submitter</span>\" => \"<span class=sf-dump-str title=\"5 characters\">apply</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-619391592\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-684017931 data-indent-pad=\"  \"><span class=sf-dump-note>array:21</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">xmetr.gc</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">343</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">https://xmetr.gc</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>dnt</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/x-www-form-urlencoded</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"36 characters\">https://xmetr.gc/admin/states/create</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"50 characters\">en-US,en;q=0.9,ur;q=0.8,pl;q=0.7,ru;q=0.6,ar;q=0.5</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1687 characters\">remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IjE5eVFoNXI3S3dCZTVpWVUvb1ZlTkE9PSIsInZhbHVlIjoiVFFEOHg1MHRNUCtIQndDL1FUdThDSVJxSzFQTTlRRWNjUGJtalZRTmpvRFQrQ1UybGVrWnR6dUN0ZU16VVJXRWg0UVg5cFdaQmlmL212RUdPSjJLcUgray8wa3JyeFU3LzUrRUs3by9jVXM1cCt0MDZCTXZjczI2RTRNR0xqeTBJT1dpcWNNQTdEeW8wYkZ1S3lUc1pZSVFCd2FiN2EvdTRmZGdiU2FOYVREVUtPdGw5SG1xOUZHVU5Db0Zwa1lvVTRXRDFPOXBhNnZOQ3hkWjJjU2JtaDRQRU9WYXRwczhPTmM1Wit2aThBcz0iLCJtYWMiOiI0NThmZWI3NDI2NThmMTIxOWRlN2JiMjljY2Y3MjQzMDUzNmQyYjA5NzllZTg4YmY2ZTc5MGExYjEzODJhMGU1IiwidGFnIjoiIn0%3D; _ga=GA1.1.2064844405.1743885089; cookie_for_consent=1; wishlist=657; _hjSessionUser_6417422=eyJpZCI6IjhkMzA2ODc1LTcwNjctNTU3Yi1hNDljLTdhNmEyNTdkM2QxOCIsImNyZWF0ZWQiOjE3NDgzODAwMDM5NTcsImV4aXN0aW5nIjp0cnVlfQ==; _hjSession_6417422=eyJpZCI6ImE5NTU4ZTg1LTkzYjItNDg3Ni04OWZiLWRiNGE2NTQyZmJlMyIsImMiOjE3NDk5MzU4MzMwNDQsInMiOjEsInIiOjAsInNiIjowLCJzciI6MCwic2UiOjAsImZzIjowfQ==; _ga_KQ6X76DET4=GS2.1.s1749935834$o64$g1$t1749935851$j43$l0$h0; XSRF-TOKEN=eyJpdiI6InB1NUNob0duRVpzT3Fxb3VYLzJjbGc9PSIsInZhbHVlIjoiNUplU0c4dFEzSkEyTTY3RWRvVmtHdlBrM3g0NlBOUkl4S3JVR1Z5YnQyUFlBTjU2QWl5am1RTjRKVGhWb21YRkNab2g3aXhzNFhGNG9Kbk5FbWlhcERuTlNiNmttT3RmMDBJMHEvSVh4TEJrbEk5TXBVUjdTQXBadWtxZjN4ZVgiLCJtYWMiOiI5ZWYzMjNlOWUzNThjZWVkYzNmNDY0YWMwNWU3NWQzYzRlZTIzZWU2OWMzODlhMDMyMGVlNmY4ZThjN2Q0NWJlIiwidGFnIjoiIn0%3D; xmetr_session=eyJpdiI6InVyWVE4ZWhrc0JwQ3IzS3kvQ3prcXc9PSIsInZhbHVlIjoiRmQzTEtPNW5DZUw5amloZXJxY0hVZ1RiSjRHSmNHMzkwYVZuczZkWGNmVDhJVjM1bFpnOGtiQ1JYS3M1Z0l2bWtnVXIxei8ySDhRdmc5L0Q4aFpJRy93aEJ4WmQ0bDk0U0tKemYxQ0c3NG96SlQ2WmhwM3pGWkk0cjROdkF5NXUiLCJtYWMiOiJkZmNmMGI2NTUwNTk3NTZiZmFkNTUxYjc5N2M1MGVkMDFhOWZkYTFiZjRkMGE2MmQ3MzMzNWU4ODkxZjJiOWQ5IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-684017931\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1827351224 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"123 characters\">1|1uQwj4bI0uiYsJi0APplQif6Zji5Zq40vBOmafc1oj6NYK3vu89uqM3GLC7O|$2y$04$Astns3SXblBceX03zRicBuVKoyIRRFaOY1GuvREgnRYhHEelfNbdu</span>\"\n  \"<span class=sf-dump-key>_ga</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_for_consent</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>wishlist</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_hjSessionUser_6417422</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_hjSession_6417422</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_ga_KQ6X76DET4</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Rjk5dM1E37zSzatTdbPstutuCWngDisDc2aBepFu</span>\"\n  \"<span class=sf-dump-key>xmetr_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">lgniq8MYOTQ2XArI9qSaENnked1vRTrQhDkCFngG</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1827351224\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1676157882 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 14 Jun 2025 21:33:21 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"36 characters\">https://xmetr.gc/admin/states/edit/1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1676157882\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-814161495 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Rjk5dM1E37zSzatTdbPstutuCWngDisDc2aBepFu</span>\"\n  \"<span class=sf-dump-key>language</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>new</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">success_msg</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>old</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"36 characters\">https://xmetr.gc/admin/states/create</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n  \"<span class=sf-dump-key>success_msg</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Created successfully</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-814161495\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "302 Found", "full_url": "https://xmetr.gc/admin/states/create", "action_name": "state.create.store", "controller_action": "Xmetr\\Location\\Http\\Controllers\\StateController@store"}, "badge": "302 Found"}}