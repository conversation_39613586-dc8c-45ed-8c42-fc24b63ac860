<?php

namespace Xmetr\Location\Models;

use Xmetr\Base\Casts\SafeContent;
use Xmetr\Base\Enums\BaseStatusEnum;
use Xmetr\Base\Models\BaseModel;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Xmetr\RealEstate\Models\Property;

class District extends BaseModel
{
    protected $table = 'districts';

    protected $fillable = [
        'name',
        'city_id',
        'state_id',
        'country_id',
        'image',
        'order',
        'is_default',
        'status',
    ];

    protected $casts = [
        'status' => BaseStatusEnum::class,
        'name' => SafeContent::class,
        'is_default' => 'bool',
        'order' => 'int',
    ];

    protected static function booted(): void
    {
        static::saving(function (self $model): void {
            $model->slug = self::createSlug($model->slug ?: $model->name, $model->getKey());
        });
    }

    public function city(): BelongsTo
    {
        return $this->belongsTo(City::class)->withDefault();
    }

    public function state(): BelongsTo
    {
        return $this->belongsTo(State::class)->withDefault();
    }

    public function country(): BelongsTo
    {
        return $this->belongsTo(Country::class)->withDefault();
    }

    public function properties()
    {
        return $this->hasMany(Property::class, 'district_id', 'id');
    }
}
