<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class () extends Migration {
    public function up(): void
    {
        if (! Schema::hasColumn('re_properties', 'district_id')) {
            Schema::table('re_properties', function (Blueprint $table): void {
                $table->foreignId('district_id')->nullable()->after('city_id');
            });
        }
    }

    public function down(): void
    {
        if (Schema::hasColumn('re_properties', 'district_id')) {
            Schema::table('re_properties', function (Blueprint $table): void {
                $table->dropColumn('district_id');
            });
        }
    }
};
