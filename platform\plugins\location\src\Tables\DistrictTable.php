<?php

namespace Xmetr\Location\Tables;

use Xmetr\Base\Facades\Html;
use Xmetr\Location\Models\District;
use Xmetr\Table\Abstracts\TableAbstract;
use Xmetr\Table\Actions\DeleteAction;
use Xmetr\Table\Actions\EditAction;
use Xmetr\Table\BulkActions\DeleteBulkAction;
use Xmetr\Table\BulkChanges\CreatedAtBulkChange;
use Xmetr\Table\BulkChanges\NameBulkChange;
use Xmetr\Table\BulkChanges\StatusBulkChange;
use Xmetr\Table\Columns\Column;
use Xmetr\Table\Columns\CreatedAtColumn;
use Xmetr\Table\Columns\IdColumn;
use Xmetr\Table\Columns\NameColumn;
use Xmetr\Table\Columns\StatusColumn;
use Xmetr\Table\HeaderActions\CreateHeaderAction;
use Illuminate\Database\Eloquent\Builder;

class DistrictTable extends TableAbstract
{
    public function setup(): void
    {
        $this
            ->model(District::class)
            ->addColumns([
                IdColumn::make(),
                NameColumn::make()->route('district.edit'),
                Column::make('city_id')
                    ->title(trans('plugins/location::district.city'))
                    ->alignStart()
                    ->renderUsing(function (District $item) {
                        if (! $item->city->name) {
                            return '&mdash;';
                        }

                        return Html::link(route('city.edit', $item->city->id), $item->city->name);
                    }),
                Column::make('state_id')
                    ->title(trans('plugins/location::district.state'))
                    ->alignStart()
                    ->renderUsing(function (District $item) {
                        if (! $item->state->name) {
                            return '&mdash;';
                        }

                        return Html::link(route('state.edit', $item->state->id), $item->state->name);
                    }),
                Column::make('country_id')
                    ->title(trans('plugins/location::district.country'))
                    ->alignStart()
                    ->renderUsing(function (District $item) {
                        if (! $item->country->name) {
                            return '&mdash;';
                        }

                        return Html::link(route('country.edit', $item->country->id), $item->country->name);
                    }),
                CreatedAtColumn::make(),
                StatusColumn::make(),
            ])
            ->addHeaderAction(CreateHeaderAction::make()->route('district.create'))
            ->addActions([
                EditAction::make()->route('district.edit'),
                DeleteAction::make()->route('district.destroy'),
            ])
            ->addBulkAction(DeleteBulkAction::make()->permission('district.destroy'))
            ->addBulkChanges([
                NameBulkChange::make(),
                StatusBulkChange::make(),
                CreatedAtBulkChange::make(),
            ])
            ->queryUsing(function (Builder $query) {
                return $query
                    ->select([
                        'id',
                        'name',
                        'city_id',
                        'state_id',
                        'country_id',
                        'created_at',
                        'status',
                    ])
                    ->with(['city', 'state', 'country']);
            });
    }

    public function getDefaultButtons(): array
    {
        return [
            'export',
            'reload',
        ];
    }


}
