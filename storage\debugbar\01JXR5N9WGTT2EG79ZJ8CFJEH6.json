{"__meta": {"id": "01JXR5N9WGTT2EG79ZJ8CFJEH6", "datetime": "2025-06-14 21:31:18", "utime": **********.800933, "method": "GET", "uri": "/admin/districts/edit/1", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749936677.239481, "end": **********.800947, "duration": 1.5614659786224365, "duration_str": "1.56s", "measures": [{"label": "Booting", "start": 1749936677.239481, "relative_start": 0, "end": **********.059548, "relative_end": **********.059548, "duration": 0.****************, "duration_str": "820ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.059562, "relative_start": 0.****************, "end": **********.800949, "relative_end": 2.1457672119140625e-06, "duration": 0.***************, "duration_str": "741ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.079743, "relative_start": 0.****************, "end": **********.097125, "relative_end": **********.097125, "duration": 0.017382144927978516, "duration_str": "17.38ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: core/base::forms.form", "start": **********.155413, "relative_start": 0.****************, "end": **********.155413, "relative_end": **********.155413, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/language::partials.notification", "start": **********.15941, "relative_start": 0.***************, "end": **********.15941, "relative_end": **********.15941, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::alert", "start": **********.163344, "relative_start": 0.***********25488, "end": **********.163344, "relative_end": **********.163344, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::920a4a8abfd99078c24a8f8db36a19f4", "start": **********.165632, "relative_start": 0.9261510372161865, "end": **********.165632, "relative_end": **********.165632, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.text", "start": **********.166818, "relative_start": 0.9273369312286377, "end": **********.166818, "relative_end": **********.166818, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.label", "start": **********.167553, "relative_start": 0.9280719757080078, "end": **********.167553, "relative_end": **********.167553, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": **********.167985, "relative_start": 0.9285039901733398, "end": **********.167985, "relative_end": **********.167985, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.168993, "relative_start": 0.9295120239257812, "end": **********.168993, "relative_end": **********.168993, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.169582, "relative_start": 0.9301009178161621, "end": **********.169582, "relative_end": **********.169582, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.171601, "relative_start": 0.9321200847625732, "end": **********.171601, "relative_end": **********.171601, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: packages/slug::forms.fields.permalink", "start": **********.173075, "relative_start": 0.9335939884185791, "end": **********.173075, "relative_end": **********.173075, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: packages/slug::permalink", "start": **********.178568, "relative_start": 0.9390869140625, "end": **********.178568, "relative_end": **********.178568, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::0dc66e2ea833d19323a8163033ac48c1", "start": **********.182532, "relative_start": 0.9430510997772217, "end": **********.182532, "relative_end": **********.182532, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.text-input", "start": **********.18293, "relative_start": 0.9434490203857422, "end": **********.18293, "relative_end": **********.18293, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.label", "start": **********.18443, "relative_start": 0.9449489116668701, "end": **********.18443, "relative_end": **********.18443, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.error", "start": **********.185069, "relative_start": 0.9455881118774414, "end": **********.185069, "relative_end": **********.185069, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form-group", "start": **********.185559, "relative_start": 0.9460780620574951, "end": **********.185559, "relative_end": **********.185559, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.helper-text", "start": **********.18619, "relative_start": 0.9467089176177979, "end": **********.18619, "relative_end": **********.18619, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.error", "start": **********.186729, "relative_start": 0.9472479820251465, "end": **********.186729, "relative_end": **********.186729, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.187043, "relative_start": 0.9475619792938232, "end": **********.187043, "relative_end": **********.187043, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.custom-select", "start": **********.187634, "relative_start": 0.948153018951416, "end": **********.187634, "relative_end": **********.187634, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.label", "start": **********.188175, "relative_start": 0.9486939907073975, "end": **********.188175, "relative_end": **********.188175, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.custom-select", "start": **********.188575, "relative_start": 0.9490940570831299, "end": **********.188575, "relative_end": **********.188575, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": **********.188996, "relative_start": 0.9495151042938232, "end": **********.188996, "relative_end": **********.188996, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.18951, "relative_start": 0.9500291347503662, "end": **********.18951, "relative_end": **********.18951, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.18978, "relative_start": 0.9502990245819092, "end": **********.18978, "relative_end": **********.18978, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.190076, "relative_start": 0.9505951404571533, "end": **********.190076, "relative_end": **********.190076, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.custom-select", "start": **********.190475, "relative_start": 0.9509940147399902, "end": **********.190475, "relative_end": **********.190475, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.label", "start": **********.190893, "relative_start": 0.9514119625091553, "end": **********.190893, "relative_end": **********.190893, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.custom-select", "start": **********.191154, "relative_start": 0.9516730308532715, "end": **********.191154, "relative_end": **********.191154, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": **********.191442, "relative_start": 0.9519610404968262, "end": **********.191442, "relative_end": **********.191442, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.191919, "relative_start": 0.9524381160736084, "end": **********.191919, "relative_end": **********.191919, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.192147, "relative_start": 0.9526660442352295, "end": **********.192147, "relative_end": **********.192147, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.192413, "relative_start": 0.9529321193695068, "end": **********.192413, "relative_end": **********.192413, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.custom-select", "start": **********.192773, "relative_start": 0.9532921314239502, "end": **********.192773, "relative_end": **********.192773, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.label", "start": **********.193174, "relative_start": 0.9536929130554199, "end": **********.193174, "relative_end": **********.193174, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.custom-select", "start": **********.193394, "relative_start": 0.9539129734039307, "end": **********.193394, "relative_end": **********.193394, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": **********.193639, "relative_start": 0.9541580677032471, "end": **********.193639, "relative_end": **********.193639, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.194089, "relative_start": 0.9546079635620117, "end": **********.194089, "relative_end": **********.194089, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.194293, "relative_start": 0.9548120498657227, "end": **********.194293, "relative_end": **********.194293, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.19455, "relative_start": 0.9550690650939941, "end": **********.19455, "relative_end": **********.19455, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.number", "start": **********.195093, "relative_start": 0.9556119441986084, "end": **********.195093, "relative_end": **********.195093, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.label", "start": **********.195556, "relative_start": 0.9560749530792236, "end": **********.195556, "relative_end": **********.195556, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": **********.19581, "relative_start": 0.9563291072845459, "end": **********.19581, "relative_end": **********.19581, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.196357, "relative_start": 0.9568760395050049, "end": **********.196357, "relative_end": **********.196357, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.196566, "relative_start": 0.957085132598877, "end": **********.196566, "relative_end": **********.196566, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.196813, "relative_start": 0.9573321342468262, "end": **********.196813, "relative_end": **********.196813, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.on-off", "start": **********.19726, "relative_start": 0.9577789306640625, "end": **********.19726, "relative_end": **********.19726, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.on-off", "start": **********.197806, "relative_start": 0.9583249092102051, "end": **********.197806, "relative_end": **********.197806, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.toggle", "start": **********.198343, "relative_start": 0.9588620662689209, "end": **********.198343, "relative_end": **********.198343, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": **********.198786, "relative_start": 0.9593050479888916, "end": **********.198786, "relative_end": **********.198786, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.199233, "relative_start": 0.959752082824707, "end": **********.199233, "relative_end": **********.199233, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.199454, "relative_start": 0.9599730968475342, "end": **********.199454, "relative_end": **********.199454, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.199705, "relative_start": 0.960223913192749, "end": **********.199705, "relative_end": **********.199705, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::card.body.index", "start": **********.200127, "relative_start": 0.9606459140777588, "end": **********.200127, "relative_end": **********.200127, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::card.index", "start": **********.200493, "relative_start": 0.9610121250152588, "end": **********.200493, "relative_end": **********.200493, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.form-content-only", "start": **********.210425, "relative_start": 0.9709439277648926, "end": **********.210425, "relative_end": **********.210425, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.text", "start": **********.211103, "relative_start": 0.9716219902038574, "end": **********.211103, "relative_end": **********.211103, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.label", "start": **********.211754, "relative_start": 0.9722731113433838, "end": **********.211754, "relative_end": **********.211754, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": **********.212069, "relative_start": 0.972588062286377, "end": **********.212069, "relative_end": **********.212069, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.212557, "relative_start": 0.9730761051177979, "end": **********.212557, "relative_end": **********.212557, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.212815, "relative_start": 0.9733340740203857, "end": **********.212815, "relative_end": **********.212815, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.21323, "relative_start": 0.9737489223480225, "end": **********.21323, "relative_end": **********.21323, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.textarea", "start": **********.213644, "relative_start": 0.9741630554199219, "end": **********.213644, "relative_end": **********.213644, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.label", "start": **********.21408, "relative_start": 0.9745991230010986, "end": **********.21408, "relative_end": **********.21408, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": **********.214341, "relative_start": 0.9748599529266357, "end": **********.214341, "relative_end": **********.214341, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.214789, "relative_start": 0.9753079414367676, "end": **********.214789, "relative_end": **********.214789, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.215021, "relative_start": 0.9755399227142334, "end": **********.215021, "relative_end": **********.215021, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.215292, "relative_start": 0.9758110046386719, "end": **********.215292, "relative_end": **********.215292, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.media-image", "start": **********.21601, "relative_start": 0.9765291213989258, "end": **********.21601, "relative_end": **********.21601, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.label", "start": **********.216733, "relative_start": 0.9772520065307617, "end": **********.216733, "relative_end": **********.216733, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.image", "start": **********.217283, "relative_start": 0.977802038192749, "end": **********.217283, "relative_end": **********.217283, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.image", "start": **********.218129, "relative_start": 0.9786479473114014, "end": **********.218129, "relative_end": **********.218129, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::image", "start": **********.220117, "relative_start": 0.9806361198425293, "end": **********.220117, "relative_end": **********.220117, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button", "start": **********.220695, "relative_start": 0.9812140464782715, "end": **********.220695, "relative_end": **********.220695, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::1c3e3c2b5f3306f2c083e21b9339fe63", "start": **********.222009, "relative_start": 0.9825279712677002, "end": **********.222009, "relative_end": **********.222009, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": **********.223245, "relative_start": 0.9837639331817627, "end": **********.223245, "relative_end": **********.223245, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.223693, "relative_start": 0.9842119216918945, "end": **********.223693, "relative_end": **********.223693, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.223933, "relative_start": 0.9844520092010498, "end": **********.223933, "relative_end": **********.223933, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.2242, "relative_start": 0.9847190380096436, "end": **********.2242, "relative_end": **********.2242, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.custom-radio", "start": **********.224606, "relative_start": 0.9851250648498535, "end": **********.224606, "relative_end": **********.224606, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.label", "start": **********.225047, "relative_start": 0.9855661392211914, "end": **********.225047, "relative_end": **********.225047, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.custom-radio", "start": **********.225396, "relative_start": 0.985914945602417, "end": **********.225396, "relative_end": **********.225396, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.radio", "start": **********.225978, "relative_start": 0.9864969253540039, "end": **********.225978, "relative_end": **********.225978, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.radio", "start": **********.226459, "relative_start": 0.9869780540466309, "end": **********.226459, "relative_end": **********.226459, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": **********.226835, "relative_start": 0.987354040145874, "end": **********.226835, "relative_end": **********.226835, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.227355, "relative_start": 0.9878740310668945, "end": **********.227355, "relative_end": **********.227355, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.227716, "relative_start": 0.9882349967956543, "end": **********.227716, "relative_end": **********.227716, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.228059, "relative_start": 0.9885780811309814, "end": **********.228059, "relative_end": **********.228059, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: packages/seo-helper::meta-box", "start": **********.229306, "relative_start": 0.9898250102996826, "end": **********.229306, "relative_end": **********.229306, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::card.actions", "start": **********.230661, "relative_start": 0.9911799430847168, "end": **********.230661, "relative_end": **********.230661, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::hr", "start": **********.238604, "relative_start": 0.9991230964660645, "end": **********.238604, "relative_end": **********.238604, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::elements.meta-box-wrap", "start": **********.239735, "relative_start": 1.0002539157867432, "end": **********.239735, "relative_end": **********.239735, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::card.title", "start": **********.240994, "relative_start": 1.0015130043029785, "end": **********.240994, "relative_end": **********.240994, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::card.header.index", "start": **********.24154, "relative_start": 1.002058982849121, "end": **********.24154, "relative_end": **********.24154, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::card.body.index", "start": **********.241926, "relative_start": 1.0024449825286865, "end": **********.241926, "relative_end": **********.241926, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::card.index", "start": **********.242222, "relative_start": 1.0027410984039307, "end": **********.242222, "relative_end": **********.242222, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::elements.meta-box", "start": **********.242701, "relative_start": 1.0032200813293457, "end": **********.242701, "relative_end": **********.242701, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.form-actions", "start": **********.243206, "relative_start": 1.0037250518798828, "end": **********.243206, "relative_end": **********.243206, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::card.title", "start": **********.244108, "relative_start": 1.004626989364624, "end": **********.244108, "relative_end": **********.244108, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::card.header.index", "start": **********.24449, "relative_start": 1.0050089359283447, "end": **********.24449, "relative_end": **********.24449, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.form-buttons", "start": **********.244935, "relative_start": 1.0054540634155273, "end": **********.244935, "relative_end": **********.244935, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button", "start": **********.245621, "relative_start": 1.0061399936676025, "end": **********.245621, "relative_end": **********.245621, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::d25e365b684c7fa9ad5ec13cfb768fc1", "start": **********.246946, "relative_start": 1.007465124130249, "end": **********.246946, "relative_end": **********.246946, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button", "start": **********.247301, "relative_start": 1.0078201293945312, "end": **********.247301, "relative_end": **********.247301, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::5db12cde11beb11dd9ef0499874ec197", "start": **********.24862, "relative_start": 1.009139060974121, "end": **********.24862, "relative_end": **********.24862, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::card.body.index", "start": **********.249131, "relative_start": 1.0096499919891357, "end": **********.249131, "relative_end": **********.249131, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::card.index", "start": **********.249369, "relative_start": 1.009887933731079, "end": **********.249369, "relative_end": **********.249369, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.breadcrumbs", "start": **********.252404, "relative_start": 1.012923002243042, "end": **********.252404, "relative_end": **********.252404, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.form-buttons", "start": **********.252769, "relative_start": 1.0132880210876465, "end": **********.252769, "relative_end": **********.252769, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button", "start": **********.253342, "relative_start": 1.0138609409332275, "end": **********.253342, "relative_end": **********.253342, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::d25e365b684c7fa9ad5ec13cfb768fc1", "start": **********.254058, "relative_start": 1.0145769119262695, "end": **********.254058, "relative_end": **********.254058, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button", "start": **********.25433, "relative_start": 1.0148489475250244, "end": **********.25433, "relative_end": **********.25433, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::5db12cde11beb11dd9ef0499874ec197", "start": **********.254974, "relative_start": 1.0154929161071777, "end": **********.254974, "relative_end": **********.254974, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/language-advanced::language-box", "start": **********.260224, "relative_start": 1.0207431316375732, "end": **********.260224, "relative_end": **********.260224, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::********************************", "start": **********.273162, "relative_start": 1.0336809158325195, "end": **********.273162, "relative_end": **********.273162, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::********************************", "start": **********.280685, "relative_start": 1.0412039756774902, "end": **********.280685, "relative_end": **********.280685, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::********************************", "start": **********.287817, "relative_start": 1.0483360290527344, "end": **********.287817, "relative_end": **********.287817, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::********************************", "start": **********.295222, "relative_start": 1.0557410717010498, "end": **********.295222, "relative_end": **********.295222, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::elements.meta-box-wrap", "start": **********.296532, "relative_start": 1.0570509433746338, "end": **********.296532, "relative_end": **********.296532, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::card.title", "start": **********.297576, "relative_start": 1.0580949783325195, "end": **********.297576, "relative_end": **********.297576, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::card.header.index", "start": **********.297974, "relative_start": 1.0584931373596191, "end": **********.297974, "relative_end": **********.297974, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::card.body.index", "start": **********.298248, "relative_start": 1.0587670803070068, "end": **********.298248, "relative_end": **********.298248, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::card.index", "start": **********.298495, "relative_start": 1.059014081954956, "end": **********.298495, "relative_end": **********.298495, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::elements.meta-box", "start": **********.298841, "relative_start": 1.0593600273132324, "end": **********.298841, "relative_end": **********.298841, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.label", "start": **********.299147, "relative_start": 1.0596659183502197, "end": **********.299147, "relative_end": **********.299147, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::card.title", "start": **********.299396, "relative_start": 1.0599150657653809, "end": **********.299396, "relative_end": **********.299396, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::card.header.index", "start": **********.299706, "relative_start": 1.060225009918213, "end": **********.299706, "relative_end": **********.299706, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.custom-select", "start": **********.300028, "relative_start": 1.060547113418579, "end": **********.300028, "relative_end": **********.300028, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.custom-select", "start": **********.300424, "relative_start": 1.0609431266784668, "end": **********.300424, "relative_end": **********.300424, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": **********.300702, "relative_start": 1.0612211227416992, "end": **********.300702, "relative_end": **********.300702, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.301177, "relative_start": 1.0616960525512695, "end": **********.301177, "relative_end": **********.301177, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.301411, "relative_start": 1.0619299411773682, "end": **********.301411, "relative_end": **********.301411, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.301686, "relative_start": 1.0622050762176514, "end": **********.301686, "relative_end": **********.301686, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::card.index", "start": **********.30194, "relative_start": 1.0624589920043945, "end": **********.30194, "relative_end": **********.30194, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.label", "start": **********.302273, "relative_start": 1.0627920627593994, "end": **********.302273, "relative_end": **********.302273, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::card.title", "start": **********.302506, "relative_start": 1.0630249977111816, "end": **********.302506, "relative_end": **********.302506, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::card.header.index", "start": **********.302804, "relative_start": 1.0633230209350586, "end": **********.302804, "relative_end": **********.302804, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.media-image", "start": **********.303071, "relative_start": 1.0635900497436523, "end": **********.303071, "relative_end": **********.303071, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.image", "start": **********.303436, "relative_start": 1.0639550685882568, "end": **********.303436, "relative_end": **********.303436, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.image", "start": **********.303763, "relative_start": 1.064281940460205, "end": **********.303763, "relative_end": **********.303763, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::image", "start": **********.304643, "relative_start": 1.065161943435669, "end": **********.304643, "relative_end": **********.304643, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button", "start": **********.30501, "relative_start": 1.0655291080474854, "end": **********.30501, "relative_end": **********.30501, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::1c3e3c2b5f3306f2c083e21b9339fe63", "start": **********.305685, "relative_start": 1.0662040710449219, "end": **********.305685, "relative_end": **********.305685, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": **********.305939, "relative_start": 1.066457986831665, "end": **********.305939, "relative_end": **********.305939, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.306447, "relative_start": 1.0669660568237305, "end": **********.306447, "relative_end": **********.306447, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.30678, "relative_start": 1.0672991275787354, "end": **********.30678, "relative_end": **********.30678, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.307107, "relative_start": 1.0676259994506836, "end": **********.307107, "relative_end": **********.307107, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::card.index", "start": **********.307384, "relative_start": 1.0679030418395996, "end": **********.307384, "relative_end": **********.307384, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::elements.meta-box", "start": **********.30846, "relative_start": 1.068979024887085, "end": **********.30846, "relative_end": **********.30846, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/js-validation::bootstrap", "start": **********.330907, "relative_start": 1.091426134109497, "end": **********.330907, "relative_end": **********.330907, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.master", "start": **********.332041, "relative_start": 1.092560052871704, "end": **********.332041, "relative_end": **********.332041, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.vertical.partials.before-content", "start": **********.333009, "relative_start": 1.0935280323028564, "end": **********.333009, "relative_end": **********.333009, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.vertical.partials.header", "start": **********.333457, "relative_start": 1.0939760208129883, "end": **********.333457, "relative_end": **********.333457, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::133aa97c11fca0f84f02ebcb9fd067dc", "start": **********.334856, "relative_start": 1.0953750610351562, "end": **********.334856, "relative_end": **********.334856, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::partials.logo", "start": **********.335263, "relative_start": 1.0957820415496826, "end": **********.335263, "relative_end": **********.335263, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::global-search.navbar-input", "start": **********.335801, "relative_start": 1.0963199138641357, "end": **********.335801, "relative_end": **********.335801, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.text-input", "start": **********.336271, "relative_start": 1.096790075302124, "end": **********.336271, "relative_end": **********.336271, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.label", "start": **********.337166, "relative_start": 1.0976850986480713, "end": **********.337166, "relative_end": **********.337166, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.error", "start": **********.33756, "relative_start": 1.098078966140747, "end": **********.33756, "relative_end": **********.33756, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form-group", "start": **********.337876, "relative_start": 1.0983951091766357, "end": **********.337876, "relative_end": **********.337876, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button", "start": **********.338811, "relative_start": 1.099329948425293, "end": **********.338811, "relative_end": **********.338811, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::dc988bb05638c97d86c3bc8a9b727e31", "start": **********.340282, "relative_start": 1.1008009910583496, "end": **********.340282, "relative_end": **********.340282, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.theme-toggle", "start": **********.340655, "relative_start": 1.1011741161346436, "end": **********.340655, "relative_end": **********.340655, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::ec711b4f47c4b42ebc81a7564c1d8d33", "start": **********.341782, "relative_start": 1.1023011207580566, "end": **********.341782, "relative_end": **********.341782, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::notification.nav-item", "start": **********.342966, "relative_start": 1.103485107421875, "end": **********.342966, "relative_end": **********.342966, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::306e03d3dc5634ee2e82192f553c6f9b", "start": **********.343822, "relative_start": 1.1043410301208496, "end": **********.343822, "relative_end": **********.343822, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.user-menu", "start": **********.347984, "relative_start": 1.1085031032562256, "end": **********.347984, "relative_end": **********.347984, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::dropdown.item", "start": **********.380235, "relative_start": 1.140753984451294, "end": **********.380235, "relative_end": **********.380235, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::2907df26a6102c24ab0c37391217b338", "start": **********.381661, "relative_start": 1.1421799659729004, "end": **********.381661, "relative_end": **********.381661, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::dropdown.item", "start": **********.38206, "relative_start": 1.1425790786743164, "end": **********.38206, "relative_end": **********.38206, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::f38bffca7b8a1a50e97a6950ffd66c5c", "start": **********.383112, "relative_start": 1.1436309814453125, "end": **********.383112, "relative_end": **********.383112, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::dropdown.index", "start": **********.3834, "relative_start": 1.1439189910888672, "end": **********.3834, "relative_end": **********.3834, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.vertical.partials.aside", "start": **********.384175, "relative_start": 1.1446940898895264, "end": **********.384175, "relative_end": **********.384175, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::133aa97c11fca0f84f02ebcb9fd067dc", "start": **********.385113, "relative_start": 1.145632028579712, "end": **********.385113, "relative_end": **********.385113, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::partials.logo", "start": **********.385344, "relative_start": 1.1458630561828613, "end": **********.385344, "relative_end": **********.385344, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::dropdown.item", "start": **********.392447, "relative_start": 1.152966022491455, "end": **********.392447, "relative_end": **********.392447, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::2907df26a6102c24ab0c37391217b338", "start": **********.393239, "relative_start": 1.1537580490112305, "end": **********.393239, "relative_end": **********.393239, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::dropdown.item", "start": **********.393595, "relative_start": 1.154114007949829, "end": **********.393595, "relative_end": **********.393595, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::f38bffca7b8a1a50e97a6950ffd66c5c", "start": **********.394408, "relative_start": 1.1549270153045654, "end": **********.394408, "relative_end": **********.394408, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::dropdown.index", "start": **********.394738, "relative_start": 1.155256986618042, "end": **********.394738, "relative_end": **********.394738, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.vertical.partials.sidebar", "start": **********.39576, "relative_start": 1.1562790870666504, "end": **********.39576, "relative_end": **********.39576, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav", "start": **********.396418, "relative_start": 1.1569371223449707, "end": **********.396418, "relative_end": **********.396418, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item", "start": **********.413507, "relative_start": 1.1740260124206543, "end": **********.413507, "relative_end": **********.413507, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.414445, "relative_start": 1.1749639511108398, "end": **********.414445, "relative_end": **********.414445, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::af68cda57c5ca67f3b8a7729953880bc", "start": **********.41617, "relative_start": 1.1766889095306396, "end": **********.41617, "relative_end": **********.41617, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item", "start": **********.417042, "relative_start": 1.1775610446929932, "end": **********.417042, "relative_end": **********.417042, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.418044, "relative_start": 1.178563117980957, "end": **********.418044, "relative_end": **********.418044, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::5def649a5a47936dda7e47db6ffcfa75", "start": **********.420182, "relative_start": 1.1807010173797607, "end": **********.420182, "relative_end": **********.420182, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.420997, "relative_start": 1.1815159320831299, "end": **********.420997, "relative_end": **********.420997, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::d890ecc3acbc4ef41a8ece9e81698457", "start": **********.422915, "relative_start": 1.183434009552002, "end": **********.422915, "relative_end": **********.422915, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.423734, "relative_start": 1.1842529773712158, "end": **********.423734, "relative_end": **********.423734, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::d890ecc3acbc4ef41a8ece9e81698457", "start": **********.42509, "relative_start": 1.1856091022491455, "end": **********.42509, "relative_end": **********.42509, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.425886, "relative_start": 1.1864049434661865, "end": **********.425886, "relative_end": **********.425886, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::d890ecc3acbc4ef41a8ece9e81698457", "start": **********.427606, "relative_start": 1.1881251335144043, "end": **********.427606, "relative_end": **********.427606, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.428246, "relative_start": 1.188765048980713, "end": **********.428246, "relative_end": **********.428246, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::d890ecc3acbc4ef41a8ece9e81698457", "start": **********.429474, "relative_start": 1.189993143081665, "end": **********.429474, "relative_end": **********.429474, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.429912, "relative_start": 1.1904311180114746, "end": **********.429912, "relative_end": **********.429912, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::d890ecc3acbc4ef41a8ece9e81698457", "start": **********.431074, "relative_start": 1.1915929317474365, "end": **********.431074, "relative_end": **********.431074, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.431535, "relative_start": 1.192054033279419, "end": **********.431535, "relative_end": **********.431535, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::d890ecc3acbc4ef41a8ece9e81698457", "start": **********.433409, "relative_start": 1.1939280033111572, "end": **********.433409, "relative_end": **********.433409, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.434427, "relative_start": 1.194946050643921, "end": **********.434427, "relative_end": **********.434427, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::d890ecc3acbc4ef41a8ece9e81698457", "start": **********.435804, "relative_start": 1.1963229179382324, "end": **********.435804, "relative_end": **********.435804, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.43657, "relative_start": 1.1970889568328857, "end": **********.43657, "relative_end": **********.43657, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::d890ecc3acbc4ef41a8ece9e81698457", "start": **********.438283, "relative_start": 1.1988019943237305, "end": **********.438283, "relative_end": **********.438283, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.438735, "relative_start": 1.199254035949707, "end": **********.438735, "relative_end": **********.438735, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::d890ecc3acbc4ef41a8ece9e81698457", "start": **********.439878, "relative_start": 1.20039701461792, "end": **********.439878, "relative_end": **********.439878, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item", "start": **********.44031, "relative_start": 1.200829029083252, "end": **********.44031, "relative_end": **********.44031, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.44089, "relative_start": 1.201409101486206, "end": **********.44089, "relative_end": **********.44089, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::dd4c2087b0a47210b5b4e3ee87ef3eca", "start": **********.442095, "relative_start": 1.2026140689849854, "end": **********.442095, "relative_end": **********.442095, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item", "start": **********.44258, "relative_start": 1.203099012374878, "end": **********.44258, "relative_end": **********.44258, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.443176, "relative_start": 1.2036950588226318, "end": **********.443176, "relative_end": **********.443176, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::998e4178ecae37dacf7321232f455f64", "start": **********.444444, "relative_start": 1.204962968826294, "end": **********.444444, "relative_end": **********.444444, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.444948, "relative_start": 1.2054669857025146, "end": **********.444948, "relative_end": **********.444948, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::06e7cfabd119917d6efbd595a344e37b", "start": **********.447573, "relative_start": 1.2080919742584229, "end": **********.447573, "relative_end": **********.447573, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.448312, "relative_start": 1.2088310718536377, "end": **********.448312, "relative_end": **********.448312, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::6e0b6ed9bf49c6ad9d02af2c7f911103", "start": **********.450376, "relative_start": 1.21089506149292, "end": **********.450376, "relative_end": **********.450376, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.451144, "relative_start": 1.211663007736206, "end": **********.451144, "relative_end": **********.451144, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::9413e731e9bb6e320e018067130903e9", "start": **********.453299, "relative_start": 1.213818073272705, "end": **********.453299, "relative_end": **********.453299, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item", "start": **********.453841, "relative_start": 1.214359998703003, "end": **********.453841, "relative_end": **********.453841, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.45445, "relative_start": 1.2149689197540283, "end": **********.45445, "relative_end": **********.45445, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::ca20cd1247722214b06db9aa7c493b27", "start": **********.456391, "relative_start": 1.2169101238250732, "end": **********.456391, "relative_end": **********.456391, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::partials.navbar.badge-count", "start": **********.457023, "relative_start": 1.2175419330596924, "end": **********.457023, "relative_end": **********.457023, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::navbar.badge-count", "start": **********.457535, "relative_start": 1.2180540561676025, "end": **********.457535, "relative_end": **********.457535, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.458003, "relative_start": 1.218522071838379, "end": **********.458003, "relative_end": **********.458003, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::d890ecc3acbc4ef41a8ece9e81698457", "start": **********.458698, "relative_start": 1.21921706199646, "end": **********.458698, "relative_end": **********.458698, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::partials.navbar.badge-count", "start": **********.459087, "relative_start": 1.2196059226989746, "end": **********.459087, "relative_end": **********.459087, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::navbar.badge-count", "start": **********.459369, "relative_start": 1.2198879718780518, "end": **********.459369, "relative_end": **********.459369, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.459726, "relative_start": 1.220245122909546, "end": **********.459726, "relative_end": **********.459726, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::d890ecc3acbc4ef41a8ece9e81698457", "start": **********.460403, "relative_start": 1.2209219932556152, "end": **********.460403, "relative_end": **********.460403, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.460829, "relative_start": 1.2213480472564697, "end": **********.460829, "relative_end": **********.460829, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::d890ecc3acbc4ef41a8ece9e81698457", "start": **********.461588, "relative_start": 1.22210693359375, "end": **********.461588, "relative_end": **********.461588, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item", "start": **********.462039, "relative_start": 1.2225580215454102, "end": **********.462039, "relative_end": **********.462039, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.462686, "relative_start": 1.2232050895690918, "end": **********.462686, "relative_end": **********.462686, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::a3acd3bd206d0793e18d491720de886c", "start": **********.465215, "relative_start": 1.225733995437622, "end": **********.465215, "relative_end": **********.465215, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item", "start": **********.465764, "relative_start": 1.226283073425293, "end": **********.465764, "relative_end": **********.465764, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.466434, "relative_start": 1.2269530296325684, "end": **********.466434, "relative_end": **********.466434, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::ff9949c19e885406625d67d91b780159", "start": **********.468334, "relative_start": 1.2288529872894287, "end": **********.468334, "relative_end": **********.468334, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::partials.navbar.badge-count", "start": **********.468786, "relative_start": 1.2293050289154053, "end": **********.468786, "relative_end": **********.468786, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::navbar.badge-count", "start": **********.469105, "relative_start": 1.2296240329742432, "end": **********.469105, "relative_end": **********.469105, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.469523, "relative_start": 1.2300419807434082, "end": **********.469523, "relative_end": **********.469523, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::d890ecc3acbc4ef41a8ece9e81698457", "start": **********.470291, "relative_start": 1.2308099269866943, "end": **********.470291, "relative_end": **********.470291, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.470726, "relative_start": 1.2312450408935547, "end": **********.470726, "relative_end": **********.470726, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::d890ecc3acbc4ef41a8ece9e81698457", "start": **********.471452, "relative_start": 1.231971025466919, "end": **********.471452, "relative_end": **********.471452, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item", "start": **********.471896, "relative_start": 1.232414960861206, "end": **********.471896, "relative_end": **********.471896, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.472484, "relative_start": 1.2330031394958496, "end": **********.472484, "relative_end": **********.472484, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::fd978891e1ac33723cbffddc6658659a", "start": **********.474265, "relative_start": 1.2347841262817383, "end": **********.474265, "relative_end": **********.474265, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.47476, "relative_start": 1.2352790832519531, "end": **********.47476, "relative_end": **********.47476, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::d890ecc3acbc4ef41a8ece9e81698457", "start": **********.475463, "relative_start": 1.2359819412231445, "end": **********.475463, "relative_end": **********.475463, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.475884, "relative_start": 1.236402988433838, "end": **********.475884, "relative_end": **********.475884, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::d890ecc3acbc4ef41a8ece9e81698457", "start": **********.476582, "relative_start": 1.2371010780334473, "end": **********.476582, "relative_end": **********.476582, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item", "start": **********.477035, "relative_start": 1.2375540733337402, "end": **********.477035, "relative_end": **********.477035, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.47762, "relative_start": 1.2381389141082764, "end": **********.47762, "relative_end": **********.47762, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::bbf20ce5b72ea2c57e1a89503478967d", "start": **********.480136, "relative_start": 1.2406549453735352, "end": **********.480136, "relative_end": **********.480136, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item", "start": **********.481042, "relative_start": 1.241560935974121, "end": **********.481042, "relative_end": **********.481042, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.481925, "relative_start": 1.2424440383911133, "end": **********.481925, "relative_end": **********.481925, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::13365b7e5a448d13150fdb4b3884b510", "start": **********.483479, "relative_start": 1.2439980506896973, "end": **********.483479, "relative_end": **********.483479, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item", "start": **********.484051, "relative_start": 1.244570016860962, "end": **********.484051, "relative_end": **********.484051, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.484729, "relative_start": 1.2452480792999268, "end": **********.484729, "relative_end": **********.484729, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::98e88d58787b8dfeb6f0d1dc0a785cfd", "start": **********.486369, "relative_start": 1.2468879222869873, "end": **********.486369, "relative_end": **********.486369, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::partials.navbar.badge-count", "start": **********.486914, "relative_start": 1.2474329471588135, "end": **********.486914, "relative_end": **********.486914, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::navbar.badge-count", "start": **********.487288, "relative_start": 1.2478070259094238, "end": **********.487288, "relative_end": **********.487288, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.487755, "relative_start": 1.2482740879058838, "end": **********.487755, "relative_end": **********.487755, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::cbce7a4c13e13a70eabb7759894a60fd", "start": **********.489232, "relative_start": 1.249751091003418, "end": **********.489232, "relative_end": **********.489232, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.489768, "relative_start": 1.2502870559692383, "end": **********.489768, "relative_end": **********.489768, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::fe6fcb7551f99a7d9d1c5a4c0011f471", "start": **********.491333, "relative_start": 1.251852035522461, "end": **********.491333, "relative_end": **********.491333, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item", "start": **********.491948, "relative_start": 1.2524669170379639, "end": **********.491948, "relative_end": **********.491948, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.492929, "relative_start": 1.2534480094909668, "end": **********.492929, "relative_end": **********.492929, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::351bdfbe842eff22e08f1df9d5f5beb1", "start": **********.496233, "relative_start": 1.2567520141601562, "end": **********.496233, "relative_end": **********.496233, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.497186, "relative_start": 1.2577049732208252, "end": **********.497186, "relative_end": **********.497186, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::d890ecc3acbc4ef41a8ece9e81698457", "start": **********.498342, "relative_start": 1.2588610649108887, "end": **********.498342, "relative_end": **********.498342, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.498851, "relative_start": 1.2593700885772705, "end": **********.498851, "relative_end": **********.498851, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::d890ecc3acbc4ef41a8ece9e81698457", "start": **********.50006, "relative_start": 1.2605791091918945, "end": **********.50006, "relative_end": **********.50006, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item", "start": **********.500535, "relative_start": 1.2610540390014648, "end": **********.500535, "relative_end": **********.500535, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.501139, "relative_start": 1.261657953262329, "end": **********.501139, "relative_end": **********.501139, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::98e88d58787b8dfeb6f0d1dc0a785cfd", "start": **********.502608, "relative_start": 1.263127088546753, "end": **********.502608, "relative_end": **********.502608, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item", "start": **********.503099, "relative_start": 1.263617992401123, "end": **********.503099, "relative_end": **********.503099, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.503739, "relative_start": 1.2642581462860107, "end": **********.503739, "relative_end": **********.503739, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::0343a1b0800146d7d9cf6a9514ec7bf4", "start": **********.505041, "relative_start": 1.2655599117279053, "end": **********.505041, "relative_end": **********.505041, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.505545, "relative_start": 1.266063928604126, "end": **********.505545, "relative_end": **********.505545, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::15422be7d0f2aaf8c8244c1e9db20ad9", "start": **********.507352, "relative_start": 1.2678711414337158, "end": **********.507352, "relative_end": **********.507352, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.507895, "relative_start": 1.26841402053833, "end": **********.507895, "relative_end": **********.507895, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::2e5e965add6d0ee3aadb02ca38e70825", "start": **********.509649, "relative_start": 1.2701680660247803, "end": **********.509649, "relative_end": **********.509649, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.510163, "relative_start": 1.2706820964813232, "end": **********.510163, "relative_end": **********.510163, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::5b8d99843e0f8eff6046d7236026b187", "start": **********.51254, "relative_start": 1.2730591297149658, "end": **********.51254, "relative_end": **********.51254, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.513533, "relative_start": 1.2740521430969238, "end": **********.513533, "relative_end": **********.513533, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::dd248739db8ba923ebfe1f426bb71a38", "start": **********.516138, "relative_start": 1.2766571044921875, "end": **********.516138, "relative_end": **********.516138, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.517082, "relative_start": 1.2776010036468506, "end": **********.517082, "relative_end": **********.517082, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::1d6f928aaf1e585d3246cb3bee8fdd45", "start": **********.519872, "relative_start": 1.280390977859497, "end": **********.519872, "relative_end": **********.519872, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.520803, "relative_start": 1.2813220024108887, "end": **********.520803, "relative_end": **********.520803, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::acbc3e3e19c70a7ebb0e7a5d98d84fbe", "start": **********.523584, "relative_start": 1.2841029167175293, "end": **********.523584, "relative_end": **********.523584, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item", "start": **********.524179, "relative_start": 1.2846980094909668, "end": **********.524179, "relative_end": **********.524179, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.524812, "relative_start": 1.2853310108184814, "end": **********.524812, "relative_end": **********.524812, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::6e0b6ed9bf49c6ad9d02af2c7f911103", "start": **********.52574, "relative_start": 1.2862589359283447, "end": **********.52574, "relative_end": **********.52574, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item", "start": **********.526243, "relative_start": 1.286761999130249, "end": **********.526243, "relative_end": **********.526243, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.527161, "relative_start": 1.28767991065979, "end": **********.527161, "relative_end": **********.527161, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::dac323985d9d2618ad252313442aaf03", "start": **********.530053, "relative_start": 1.290571928024292, "end": **********.530053, "relative_end": **********.530053, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.530629, "relative_start": 1.2911479473114014, "end": **********.530629, "relative_end": **********.530629, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::298cd8a12b86a6f371ff06491a0822fa", "start": **********.532226, "relative_start": 1.2927451133728027, "end": **********.532226, "relative_end": **********.532226, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.532833, "relative_start": 1.2933521270751953, "end": **********.532833, "relative_end": **********.532833, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::c59870a61b233f0766e3260625bdb025", "start": **********.535014, "relative_start": 1.2955329418182373, "end": **********.535014, "relative_end": **********.535014, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.536017, "relative_start": 1.2965359687805176, "end": **********.536017, "relative_end": **********.536017, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::3890eca5d46a147ef99ddf994453d2ec", "start": **********.538407, "relative_start": 1.2989261150360107, "end": **********.538407, "relative_end": **********.538407, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.539293, "relative_start": 1.2998120784759521, "end": **********.539293, "relative_end": **********.539293, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::ff1fde71531b073b2c658173537aaea5", "start": **********.541286, "relative_start": 1.301805019378662, "end": **********.541286, "relative_end": **********.541286, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.542087, "relative_start": 1.3026061058044434, "end": **********.542087, "relative_end": **********.542087, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::5cef0de51e1489c31c7fcb5d7f2f6a97", "start": **********.544437, "relative_start": 1.3049559593200684, "end": **********.544437, "relative_end": **********.544437, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.5453, "relative_start": 1.305819034576416, "end": **********.5453, "relative_end": **********.5453, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::89cb89d3fdb0a0a12f8aa61073c231e6", "start": **********.547289, "relative_start": 1.3078079223632812, "end": **********.547289, "relative_end": **********.547289, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.54807, "relative_start": 1.308588981628418, "end": **********.54807, "relative_end": **********.54807, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::5a5b09d3f2ee0ddb2b536feb532d230a", "start": **********.550037, "relative_start": 1.3105559349060059, "end": **********.550037, "relative_end": **********.550037, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.550812, "relative_start": 1.311331033706665, "end": **********.550812, "relative_end": **********.550812, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::fedc652debeb23dcbb31a98830baa397", "start": **********.55247, "relative_start": 1.3129889965057373, "end": **********.55247, "relative_end": **********.55247, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item", "start": **********.553063, "relative_start": 1.313581943511963, "end": **********.553063, "relative_end": **********.553063, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.553709, "relative_start": 1.3142280578613281, "end": **********.553709, "relative_end": **********.553709, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::ff3b2cf4e42e74e63db76ff05c5f2374", "start": **********.555563, "relative_start": 1.3160820007324219, "end": **********.555563, "relative_end": **********.555563, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item", "start": **********.556094, "relative_start": 1.316612958908081, "end": **********.556094, "relative_end": **********.556094, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.556698, "relative_start": 1.3172171115875244, "end": **********.556698, "relative_end": **********.556698, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::745871da7c635a3f461dfaeeef54a48e", "start": **********.557969, "relative_start": 1.3184881210327148, "end": **********.557969, "relative_end": **********.557969, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.558476, "relative_start": 1.3189949989318848, "end": **********.558476, "relative_end": **********.558476, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::1d6f928aaf1e585d3246cb3bee8fdd45", "start": **********.560543, "relative_start": 1.3210620880126953, "end": **********.560543, "relative_end": **********.560543, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item", "start": **********.561364, "relative_start": 1.321882963180542, "end": **********.561364, "relative_end": **********.561364, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.562436, "relative_start": 1.3229551315307617, "end": **********.562436, "relative_end": **********.562436, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::2b3233eda7e50501ef45fd875b12da49", "start": **********.565079, "relative_start": 1.3255980014801025, "end": **********.565079, "relative_end": **********.565079, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item", "start": **********.566043, "relative_start": 1.3265619277954102, "end": **********.566043, "relative_end": **********.566043, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.56694, "relative_start": 1.3274590969085693, "end": **********.56694, "relative_end": **********.56694, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::b13663c834a4ae876ef8f72aa0610e8c", "start": **********.568516, "relative_start": 1.3290350437164307, "end": **********.568516, "relative_end": **********.568516, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.page-header", "start": **********.569413, "relative_start": 1.3299319744110107, "end": **********.569413, "relative_end": **********.569413, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::breadcrumb", "start": **********.569903, "relative_start": 1.3304219245910645, "end": **********.569903, "relative_end": **********.569903, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.footer", "start": **********.570878, "relative_start": 1.3313970565795898, "end": **********.570878, "relative_end": **********.570878, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::partials.copyright", "start": **********.57134, "relative_start": 1.3318591117858887, "end": **********.57134, "relative_end": **********.57134, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.vertical.partials.after-content", "start": **********.572518, "relative_start": 1.3330371379852295, "end": **********.572518, "relative_end": **********.572518, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::global-search.form", "start": **********.573165, "relative_start": 1.333683967590332, "end": **********.573165, "relative_end": **********.573165, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::3cec1c87224222bda738c53f782c5bc1", "start": **********.575025, "relative_start": 1.3355441093444824, "end": **********.575025, "relative_end": **********.575025, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.text-input", "start": **********.575287, "relative_start": 1.335806131362915, "end": **********.575287, "relative_end": **********.575287, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.label", "start": **********.576201, "relative_start": 1.3367199897766113, "end": **********.576201, "relative_end": **********.576201, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.error", "start": **********.576604, "relative_start": 1.337122917175293, "end": **********.576604, "relative_end": **********.576604, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form-group", "start": **********.57695, "relative_start": 1.3374691009521484, "end": **********.57695, "relative_end": **********.57695, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.index", "start": **********.57731, "relative_start": 1.3378291130065918, "end": **********.57731, "relative_end": **********.57731, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::414e4e803eeec6389552bb46515583c5", "start": **********.578343, "relative_start": 1.3388619422912598, "end": **********.578343, "relative_end": **********.578343, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::c47a448d99d5719cb034f7947c739ff8", "start": **********.579308, "relative_start": 1.339827060699463, "end": **********.579308, "relative_end": **********.579308, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::e226165e1fca7eccb10f6857d7cd235a", "start": **********.580313, "relative_start": 1.340831995010376, "end": **********.580313, "relative_end": **********.580313, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::modal", "start": **********.580711, "relative_start": 1.3412299156188965, "end": **********.580711, "relative_end": **********.580711, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::custom-template", "start": **********.581916, "relative_start": 1.3424351215362549, "end": **********.581916, "relative_end": **********.581916, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/media::partials.media", "start": **********.582817, "relative_start": 1.3433361053466797, "end": **********.582817, "relative_end": **********.582817, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::modal.close-button", "start": **********.584502, "relative_start": 1.3450210094451904, "end": **********.584502, "relative_end": **********.584502, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::loading", "start": **********.585107, "relative_start": 1.3456261157989502, "end": **********.585107, "relative_end": **********.585107, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.text-input", "start": **********.585768, "relative_start": 1.3462870121002197, "end": **********.585768, "relative_end": **********.585768, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.label", "start": **********.587242, "relative_start": 1.3477609157562256, "end": **********.587242, "relative_end": **********.587242, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.error", "start": **********.587814, "relative_start": 1.3483331203460693, "end": **********.587814, "relative_end": **********.587814, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form-group", "start": **********.588255, "relative_start": 1.3487739562988281, "end": **********.588255, "relative_end": **********.588255, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.checkbox", "start": **********.588979, "relative_start": 1.3494980335235596, "end": **********.588979, "relative_end": **********.588979, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button", "start": **********.591172, "relative_start": 1.3516910076141357, "end": **********.591172, "relative_end": **********.591172, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button", "start": **********.592315, "relative_start": 1.3528339862823486, "end": **********.592315, "relative_end": **********.592315, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::modal", "start": **********.593279, "relative_start": 1.3537979125976562, "end": **********.593279, "relative_end": **********.593279, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::modal.close-button", "start": **********.594604, "relative_start": 1.3551230430603027, "end": **********.594604, "relative_end": **********.594604, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/media::config", "start": **********.595341, "relative_start": 1.3558599948883057, "end": **********.595341, "relative_end": **********.595341, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::debug-badge", "start": **********.764535, "relative_start": 1.5250539779663086, "end": **********.764535, "relative_end": **********.764535, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::modal.action", "start": **********.765813, "relative_start": 1.526332139968872, "end": **********.765813, "relative_end": **********.765813, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::modal.alert", "start": **********.767246, "relative_start": 1.5277650356292725, "end": **********.767246, "relative_end": **********.767246, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::modal.close-button", "start": **********.768436, "relative_start": 1.5289549827575684, "end": **********.768436, "relative_end": **********.768436, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::dcf17957c4aa053a618fd9c312cc29fc", "start": **********.769446, "relative_start": 1.5299649238586426, "end": **********.769446, "relative_end": **********.769446, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::modal", "start": **********.769837, "relative_start": 1.5303559303283691, "end": **********.769837, "relative_end": **********.769837, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::modal.action", "start": **********.771351, "relative_start": 1.5318701267242432, "end": **********.771351, "relative_end": **********.771351, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::modal.alert", "start": **********.772354, "relative_start": 1.5328729152679443, "end": **********.772354, "relative_end": **********.772354, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::modal.close-button", "start": **********.773445, "relative_start": 1.533963918685913, "end": **********.773445, "relative_end": **********.773445, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::fcfb9f9ba5fb460899f38b71f491e1fe", "start": **********.774539, "relative_start": 1.5350580215454102, "end": **********.774539, "relative_end": **********.774539, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::modal", "start": **********.77487, "relative_start": 1.5353889465332031, "end": **********.77487, "relative_end": **********.77487, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::layouts.base", "start": **********.776221, "relative_start": 1.5367400646209717, "end": **********.776221, "relative_end": **********.776221, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::components.layouts.header", "start": **********.777492, "relative_start": 1.538011074066162, "end": **********.777492, "relative_end": **********.777492, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: assets::header", "start": **********.791512, "relative_start": 1.5520310401916504, "end": **********.791512, "relative_end": **********.791512, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::elements.common", "start": **********.793462, "relative_start": 1.553981065750122, "end": **********.793462, "relative_end": **********.793462, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: assets::footer", "start": **********.795911, "relative_start": 1.5564301013946533, "end": **********.795911, "relative_end": **********.795911, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::notification.notification", "start": **********.796946, "relative_start": 1.5574650764465332, "end": **********.796946, "relative_end": **********.796946, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "Preparing Response", "start": **********.798277, "relative_start": 1.5587959289550781, "end": **********.798956, "relative_end": **********.798956, "duration": 0.00067901611328125, "duration_str": "679μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": 59358144, "peak_usage_str": "57MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "10.x", "tooltip": {"Laravel Version": "10.48.29", "PHP Version": "8.3.17", "Environment": "local", "Debug Mode": "Enabled", "URL": "xmetr.gc", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 374, "nb_templates": 374, "templates": [{"name": "1x core/base::forms.form", "param_count": null, "params": [], "start": **********.155386, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/forms/form.blade.phpcore/base::forms.form", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Fform.blade.php&line=1", "ajax": false, "filename": "form.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::forms.form"}, {"name": "1x plugins/language::partials.notification", "param_count": null, "params": [], "start": **********.159386, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/plugins/language/resources/views/partials/notification.blade.phpplugins/language::partials.notification", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Flanguage%2Fresources%2Fviews%2Fpartials%2Fnotification.blade.php&line=1", "ajax": false, "filename": "notification.blade.php", "line": "?"}, "render_count": 1, "name_original": "plugins/language::partials.notification"}, {"name": "1x ********************************::alert", "param_count": null, "params": [], "start": **********.163302, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/components/alert.blade.php********************************::alert", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Falert.blade.php&line=1", "ajax": false, "filename": "alert.blade.php", "line": "?"}, "render_count": 1, "name_original": "********************************::alert"}, {"name": "1x __components::920a4a8abfd99078c24a8f8db36a19f4", "param_count": null, "params": [], "start": **********.165611, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\storage\\framework\\views/920a4a8abfd99078c24a8f8db36a19f4.blade.php__components::920a4a8abfd99078c24a8f8db36a19f4", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fstorage%2Fframework%2Fviews%2F920a4a8abfd99078c24a8f8db36a19f4.blade.php&line=1", "ajax": false, "filename": "920a4a8abfd99078c24a8f8db36a19f4.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::920a4a8abfd99078c24a8f8db36a19f4"}, {"name": "2x core/base::forms.fields.text", "param_count": null, "params": [], "start": **********.166799, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/forms/fields/text.blade.phpcore/base::forms.fields.text", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Ffields%2Ftext.blade.php&line=1", "ajax": false, "filename": "text.blade.php", "line": "?"}, "render_count": 2, "name_original": "core/base::forms.fields.text"}, {"name": "11x core/base::forms.partials.label", "param_count": null, "params": [], "start": **********.167533, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/forms/partials/label.blade.phpcore/base::forms.partials.label", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Fpartials%2Flabel.blade.php&line=1", "ajax": false, "filename": "label.blade.php", "line": "?"}, "render_count": 11, "name_original": "core/base::forms.partials.label"}, {"name": "12x ********************************::form.field", "param_count": null, "params": [], "start": **********.167965, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/components/form/field.blade.php********************************::form.field", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fform%2Ffield.blade.php&line=1", "ajax": false, "filename": "field.blade.php", "line": "?"}, "render_count": 12, "name_original": "********************************::form.field"}, {"name": "12x core/base::forms.partials.help-block", "param_count": null, "params": [], "start": **********.168956, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/forms/partials/help-block.blade.phpcore/base::forms.partials.help-block", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Fpartials%2Fhelp-block.blade.php&line=1", "ajax": false, "filename": "help-block.blade.php", "line": "?"}, "render_count": 12, "name_original": "core/base::forms.partials.help-block"}, {"name": "12x core/base::forms.partials.errors", "param_count": null, "params": [], "start": **********.169548, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/forms/partials/errors.blade.phpcore/base::forms.partials.errors", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Fpartials%2Ferrors.blade.php&line=1", "ajax": false, "filename": "errors.blade.php", "line": "?"}, "render_count": 12, "name_original": "core/base::forms.partials.errors"}, {"name": "13x core/base::forms.columns.column-span", "param_count": null, "params": [], "start": **********.171565, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/forms/columns/column-span.blade.phpcore/base::forms.columns.column-span", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Fcolumns%2Fcolumn-span.blade.php&line=1", "ajax": false, "filename": "column-span.blade.php", "line": "?"}, "render_count": 13, "name_original": "core/base::forms.columns.column-span"}, {"name": "1x packages/slug::forms.fields.permalink", "param_count": null, "params": [], "start": **********.173039, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/packages/slug/resources/views/forms/fields/permalink.blade.phppackages/slug::forms.fields.permalink", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fpackages%2Fslug%2Fresources%2Fviews%2Fforms%2Ffields%2Fpermalink.blade.php&line=1", "ajax": false, "filename": "permalink.blade.php", "line": "?"}, "render_count": 1, "name_original": "packages/slug::forms.fields.permalink"}, {"name": "1x packages/slug::permalink", "param_count": null, "params": [], "start": **********.178546, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/packages/slug/resources/views/permalink.blade.phppackages/slug::permalink", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fpackages%2Fslug%2Fresources%2Fviews%2Fpermalink.blade.php&line=1", "ajax": false, "filename": "permalink.blade.php", "line": "?"}, "render_count": 1, "name_original": "packages/slug::permalink"}, {"name": "1x __components::0dc66e2ea833d19323a8163033ac48c1", "param_count": null, "params": [], "start": **********.182505, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\storage\\framework\\views/0dc66e2ea833d19323a8163033ac48c1.blade.php__components::0dc66e2ea833d19323a8163033ac48c1", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fstorage%2Fframework%2Fviews%2F0dc66e2ea833d19323a8163033ac48c1.blade.php&line=1", "ajax": false, "filename": "0dc66e2ea833d19323a8163033ac48c1.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::0dc66e2ea833d19323a8163033ac48c1"}, {"name": "4x ********************************::form.text-input", "param_count": null, "params": [], "start": **********.182907, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/components/form/text-input.blade.php********************************::form.text-input", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fform%2Ftext-input.blade.php&line=1", "ajax": false, "filename": "text-input.blade.php", "line": "?"}, "render_count": 4, "name_original": "********************************::form.text-input"}, {"name": "4x ********************************::form.label", "param_count": null, "params": [], "start": **********.184405, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/components/form/label.blade.php********************************::form.label", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fform%2Flabel.blade.php&line=1", "ajax": false, "filename": "label.blade.php", "line": "?"}, "render_count": 4, "name_original": "********************************::form.label"}, {"name": "4x ********************************::form.error", "param_count": null, "params": [], "start": **********.185048, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/components/form/error.blade.php********************************::form.error", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fform%2Ferror.blade.php&line=1", "ajax": false, "filename": "error.blade.php", "line": "?"}, "render_count": 4, "name_original": "********************************::form.error"}, {"name": "4x ********************************::form-group", "param_count": null, "params": [], "start": **********.185539, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/components/form-group.blade.php********************************::form-group", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fform-group.blade.php&line=1", "ajax": false, "filename": "form-group.blade.php", "line": "?"}, "render_count": 4, "name_original": "********************************::form-group"}, {"name": "1x ********************************::form.helper-text", "param_count": null, "params": [], "start": **********.186168, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/components/form/helper-text.blade.php********************************::form.helper-text", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fform%2Fhelper-text.blade.php&line=1", "ajax": false, "filename": "helper-text.blade.php", "line": "?"}, "render_count": 1, "name_original": "********************************::form.helper-text"}, {"name": "1x core/base::forms.partials.error", "param_count": null, "params": [], "start": **********.186709, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/forms/partials/error.blade.phpcore/base::forms.partials.error", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Fpartials%2Ferror.blade.php&line=1", "ajax": false, "filename": "error.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::forms.partials.error"}, {"name": "4x core/base::forms.fields.custom-select", "param_count": null, "params": [], "start": **********.187613, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/forms/fields/custom-select.blade.phpcore/base::forms.fields.custom-select", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Ffields%2Fcustom-select.blade.php&line=1", "ajax": false, "filename": "custom-select.blade.php", "line": "?"}, "render_count": 4, "name_original": "core/base::forms.fields.custom-select"}, {"name": "4x core/base::forms.partials.custom-select", "param_count": null, "params": [], "start": **********.188555, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/forms/partials/custom-select.blade.phpcore/base::forms.partials.custom-select", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Fpartials%2Fcustom-select.blade.php&line=1", "ajax": false, "filename": "custom-select.blade.php", "line": "?"}, "render_count": 4, "name_original": "core/base::forms.partials.custom-select"}, {"name": "1x core/base::forms.fields.number", "param_count": null, "params": [], "start": **********.195073, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/forms/fields/number.blade.phpcore/base::forms.fields.number", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Ffields%2Fnumber.blade.php&line=1", "ajax": false, "filename": "number.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::forms.fields.number"}, {"name": "1x core/base::forms.fields.on-off", "param_count": null, "params": [], "start": **********.19724, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/forms/fields/on-off.blade.phpcore/base::forms.fields.on-off", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Ffields%2Fon-off.blade.php&line=1", "ajax": false, "filename": "on-off.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::forms.fields.on-off"}, {"name": "1x core/base::forms.partials.on-off", "param_count": null, "params": [], "start": **********.197786, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/forms/partials/on-off.blade.phpcore/base::forms.partials.on-off", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Fpartials%2Fon-off.blade.php&line=1", "ajax": false, "filename": "on-off.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::forms.partials.on-off"}, {"name": "1x ********************************::form.toggle", "param_count": null, "params": [], "start": **********.198324, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/components/form/toggle.blade.php********************************::form.toggle", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fform%2Ftoggle.blade.php&line=1", "ajax": false, "filename": "toggle.blade.php", "line": "?"}, "render_count": 1, "name_original": "********************************::form.toggle"}, {"name": "4x ********************************::card.body.index", "param_count": null, "params": [], "start": **********.200107, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/components/card/body/index.blade.php********************************::card.body.index", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fcard%2Fbody%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 4, "name_original": "********************************::card.body.index"}, {"name": "6x ********************************::card.index", "param_count": null, "params": [], "start": **********.200474, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/components/card/index.blade.php********************************::card.index", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fcard%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 6, "name_original": "********************************::card.index"}, {"name": "1x core/base::forms.form-content-only", "param_count": null, "params": [], "start": **********.210403, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/forms/form-content-only.blade.phpcore/base::forms.form-content-only", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Fform-content-only.blade.php&line=1", "ajax": false, "filename": "form-content-only.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::forms.form-content-only"}, {"name": "1x core/base::forms.fields.textarea", "param_count": null, "params": [], "start": **********.213624, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/forms/fields/textarea.blade.phpcore/base::forms.fields.textarea", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Ffields%2Ftextarea.blade.php&line=1", "ajax": false, "filename": "textarea.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::forms.fields.textarea"}, {"name": "2x core/base::forms.fields.media-image", "param_count": null, "params": [], "start": **********.215989, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/forms/fields/media-image.blade.phpcore/base::forms.fields.media-image", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Ffields%2Fmedia-image.blade.php&line=1", "ajax": false, "filename": "media-image.blade.php", "line": "?"}, "render_count": 2, "name_original": "core/base::forms.fields.media-image"}, {"name": "2x core/base::forms.partials.image", "param_count": null, "params": [], "start": **********.217263, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/forms/partials/image.blade.phpcore/base::forms.partials.image", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Fpartials%2Fimage.blade.php&line=1", "ajax": false, "filename": "image.blade.php", "line": "?"}, "render_count": 2, "name_original": "core/base::forms.partials.image"}, {"name": "2x ********************************::form.image", "param_count": null, "params": [], "start": **********.218108, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/components/form/image.blade.php********************************::form.image", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fform%2Fimage.blade.php&line=1", "ajax": false, "filename": "image.blade.php", "line": "?"}, "render_count": 2, "name_original": "********************************::form.image"}, {"name": "2x ********************************::image", "param_count": null, "params": [], "start": **********.220097, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/components/image.blade.php********************************::image", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fimage.blade.php&line=1", "ajax": false, "filename": "image.blade.php", "line": "?"}, "render_count": 2, "name_original": "********************************::image"}, {"name": "9x ********************************::button", "param_count": null, "params": [], "start": **********.220675, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/components/button.blade.php********************************::button", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fbutton.blade.php&line=1", "ajax": false, "filename": "button.blade.php", "line": "?"}, "render_count": 9, "name_original": "********************************::button"}, {"name": "2x __components::1c3e3c2b5f3306f2c083e21b9339fe63", "param_count": null, "params": [], "start": **********.22199, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\storage\\framework\\views/1c3e3c2b5f3306f2c083e21b9339fe63.blade.php__components::1c3e3c2b5f3306f2c083e21b9339fe63", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fstorage%2Fframework%2Fviews%2F1c3e3c2b5f3306f2c083e21b9339fe63.blade.php&line=1", "ajax": false, "filename": "1c3e3c2b5f3306f2c083e21b9339fe63.blade.php", "line": "?"}, "render_count": 2, "name_original": "__components::1c3e3c2b5f3306f2c083e21b9339fe63"}, {"name": "1x core/base::forms.fields.custom-radio", "param_count": null, "params": [], "start": **********.224587, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/forms/fields/custom-radio.blade.phpcore/base::forms.fields.custom-radio", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Ffields%2Fcustom-radio.blade.php&line=1", "ajax": false, "filename": "custom-radio.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::forms.fields.custom-radio"}, {"name": "1x core/base::forms.partials.custom-radio", "param_count": null, "params": [], "start": **********.225376, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/forms/partials/custom-radio.blade.phpcore/base::forms.partials.custom-radio", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Fpartials%2Fcustom-radio.blade.php&line=1", "ajax": false, "filename": "custom-radio.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::forms.partials.custom-radio"}, {"name": "2x ********************************::form.radio", "param_count": null, "params": [], "start": **********.225958, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/components/form/radio.blade.php********************************::form.radio", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fform%2Fradio.blade.php&line=1", "ajax": false, "filename": "radio.blade.php", "line": "?"}, "render_count": 2, "name_original": "********************************::form.radio"}, {"name": "1x packages/seo-helper::meta-box", "param_count": null, "params": [], "start": **********.229283, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/packages/seo-helper/resources/views/meta-box.blade.phppackages/seo-helper::meta-box", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fpackages%2Fseo-helper%2Fresources%2Fviews%2Fmeta-box.blade.php&line=1", "ajax": false, "filename": "meta-box.blade.php", "line": "?"}, "render_count": 1, "name_original": "packages/seo-helper::meta-box"}, {"name": "1x ********************************::card.actions", "param_count": null, "params": [], "start": **********.230638, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/components/card/actions.blade.php********************************::card.actions", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fcard%2Factions.blade.php&line=1", "ajax": false, "filename": "actions.blade.php", "line": "?"}, "render_count": 1, "name_original": "********************************::card.actions"}, {"name": "1x ********************************::hr", "param_count": null, "params": [], "start": **********.23857, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/components/hr.blade.php********************************::hr", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fhr.blade.php&line=1", "ajax": false, "filename": "hr.blade.php", "line": "?"}, "render_count": 1, "name_original": "********************************::hr"}, {"name": "2x core/base::elements.meta-box-wrap", "param_count": null, "params": [], "start": **********.239713, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/elements/meta-box-wrap.blade.phpcore/base::elements.meta-box-wrap", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Felements%2Fmeta-box-wrap.blade.php&line=1", "ajax": false, "filename": "meta-box-wrap.blade.php", "line": "?"}, "render_count": 2, "name_original": "core/base::elements.meta-box-wrap"}, {"name": "5x ********************************::card.title", "param_count": null, "params": [], "start": **********.240973, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/components/card/title.blade.php********************************::card.title", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fcard%2Ftitle.blade.php&line=1", "ajax": false, "filename": "title.blade.php", "line": "?"}, "render_count": 5, "name_original": "********************************::card.title"}, {"name": "5x ********************************::card.header.index", "param_count": null, "params": [], "start": **********.241519, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/components/card/header/index.blade.php********************************::card.header.index", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fcard%2Fheader%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 5, "name_original": "********************************::card.header.index"}, {"name": "3x core/base::elements.meta-box", "param_count": null, "params": [], "start": **********.242668, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/elements/meta-box.blade.phpcore/base::elements.meta-box", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Felements%2Fmeta-box.blade.php&line=1", "ajax": false, "filename": "meta-box.blade.php", "line": "?"}, "render_count": 3, "name_original": "core/base::elements.meta-box"}, {"name": "1x core/base::forms.partials.form-actions", "param_count": null, "params": [], "start": **********.243184, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/forms/partials/form-actions.blade.phpcore/base::forms.partials.form-actions", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Fpartials%2Fform-actions.blade.php&line=1", "ajax": false, "filename": "form-actions.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::forms.partials.form-actions"}, {"name": "2x core/base::forms.partials.form-buttons", "param_count": null, "params": [], "start": **********.244913, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/forms/partials/form-buttons.blade.phpcore/base::forms.partials.form-buttons", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Fpartials%2Fform-buttons.blade.php&line=1", "ajax": false, "filename": "form-buttons.blade.php", "line": "?"}, "render_count": 2, "name_original": "core/base::forms.partials.form-buttons"}, {"name": "2x __components::d25e365b684c7fa9ad5ec13cfb768fc1", "param_count": null, "params": [], "start": **********.246925, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\storage\\framework\\views/d25e365b684c7fa9ad5ec13cfb768fc1.blade.php__components::d25e365b684c7fa9ad5ec13cfb768fc1", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fstorage%2Fframework%2Fviews%2Fd25e365b684c7fa9ad5ec13cfb768fc1.blade.php&line=1", "ajax": false, "filename": "d25e365b684c7fa9ad5ec13cfb768fc1.blade.php", "line": "?"}, "render_count": 2, "name_original": "__components::d25e365b684c7fa9ad5ec13cfb768fc1"}, {"name": "2x __components::5db12cde11beb11dd9ef0499874ec197", "param_count": null, "params": [], "start": **********.248599, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\storage\\framework\\views/5db12cde11beb11dd9ef0499874ec197.blade.php__components::5db12cde11beb11dd9ef0499874ec197", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fstorage%2Fframework%2Fviews%2F5db12cde11beb11dd9ef0499874ec197.blade.php&line=1", "ajax": false, "filename": "5db12cde11beb11dd9ef0499874ec197.blade.php", "line": "?"}, "render_count": 2, "name_original": "__components::5db12cde11beb11dd9ef0499874ec197"}, {"name": "1x core/base::layouts.partials.breadcrumbs", "param_count": null, "params": [], "start": **********.252383, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/layouts/partials/breadcrumbs.blade.phpcore/base::layouts.partials.breadcrumbs", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Flayouts%2Fpartials%2Fbreadcrumbs.blade.php&line=1", "ajax": false, "filename": "breadcrumbs.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::layouts.partials.breadcrumbs"}, {"name": "1x plugins/language-advanced::language-box", "param_count": null, "params": [], "start": **********.260203, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/plugins/language-advanced/resources/views/language-box.blade.phpplugins/language-advanced::language-box", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Flanguage-advanced%2Fresources%2Fviews%2Flanguage-box.blade.php&line=1", "ajax": false, "filename": "language-box.blade.php", "line": "?"}, "render_count": 1, "name_original": "plugins/language-advanced::language-box"}, {"name": "4x __components::********************************", "param_count": null, "params": [], "start": **********.273129, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\storage\\framework\\views/********************************.blade.php__components::********************************", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fstorage%2Fframework%2Fviews%2F********************************.blade.php&line=1", "ajax": false, "filename": "********************************.blade.php", "line": "?"}, "render_count": 4, "name_original": "__components::********************************"}, {"name": "1x core/js-validation::bootstrap", "param_count": null, "params": [], "start": **********.330883, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/js-validation/resources/views/bootstrap.blade.phpcore/js-validation::bootstrap", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fjs-validation%2Fresources%2Fviews%2Fbootstrap.blade.php&line=1", "ajax": false, "filename": "bootstrap.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/js-validation::bootstrap"}, {"name": "1x core/base::layouts.master", "param_count": null, "params": [], "start": **********.33202, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/layouts/master.blade.phpcore/base::layouts.master", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Flayouts%2Fmaster.blade.php&line=1", "ajax": false, "filename": "master.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::layouts.master"}, {"name": "1x core/base::layouts.vertical.partials.before-content", "param_count": null, "params": [], "start": **********.332988, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/layouts/vertical/partials/before-content.blade.phpcore/base::layouts.vertical.partials.before-content", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Flayouts%2Fvertical%2Fpartials%2Fbefore-content.blade.php&line=1", "ajax": false, "filename": "before-content.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::layouts.vertical.partials.before-content"}, {"name": "1x core/base::layouts.vertical.partials.header", "param_count": null, "params": [], "start": **********.333436, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/layouts/vertical/partials/header.blade.phpcore/base::layouts.vertical.partials.header", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Flayouts%2Fvertical%2Fpartials%2Fheader.blade.php&line=1", "ajax": false, "filename": "header.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::layouts.vertical.partials.header"}, {"name": "2x __components::133aa97c11fca0f84f02ebcb9fd067dc", "param_count": null, "params": [], "start": **********.334836, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\storage\\framework\\views/133aa97c11fca0f84f02ebcb9fd067dc.blade.php__components::133aa97c11fca0f84f02ebcb9fd067dc", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fstorage%2Fframework%2Fviews%2F133aa97c11fca0f84f02ebcb9fd067dc.blade.php&line=1", "ajax": false, "filename": "133aa97c11fca0f84f02ebcb9fd067dc.blade.php", "line": "?"}, "render_count": 2, "name_original": "__components::133aa97c11fca0f84f02ebcb9fd067dc"}, {"name": "2x core/base::partials.logo", "param_count": null, "params": [], "start": **********.335244, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/partials/logo.blade.phpcore/base::partials.logo", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fpartials%2Flogo.blade.php&line=1", "ajax": false, "filename": "logo.blade.php", "line": "?"}, "render_count": 2, "name_original": "core/base::partials.logo"}, {"name": "1x core/base::global-search.navbar-input", "param_count": null, "params": [], "start": **********.335781, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/global-search/navbar-input.blade.phpcore/base::global-search.navbar-input", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fglobal-search%2Fnavbar-input.blade.php&line=1", "ajax": false, "filename": "navbar-input.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::global-search.navbar-input"}, {"name": "1x __components::dc988bb05638c97d86c3bc8a9b727e31", "param_count": null, "params": [], "start": **********.340262, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\storage\\framework\\views/dc988bb05638c97d86c3bc8a9b727e31.blade.php__components::dc988bb05638c97d86c3bc8a9b727e31", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fstorage%2Fframework%2Fviews%2Fdc988bb05638c97d86c3bc8a9b727e31.blade.php&line=1", "ajax": false, "filename": "dc988bb05638c97d86c3bc8a9b727e31.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::dc988bb05638c97d86c3bc8a9b727e31"}, {"name": "1x core/base::layouts.partials.theme-toggle", "param_count": null, "params": [], "start": **********.340635, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/layouts/partials/theme-toggle.blade.phpcore/base::layouts.partials.theme-toggle", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Flayouts%2Fpartials%2Ftheme-toggle.blade.php&line=1", "ajax": false, "filename": "theme-toggle.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::layouts.partials.theme-toggle"}, {"name": "1x __components::ec711b4f47c4b42ebc81a7564c1d8d33", "param_count": null, "params": [], "start": **********.341763, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\storage\\framework\\views/ec711b4f47c4b42ebc81a7564c1d8d33.blade.php__components::ec711b4f47c4b42ebc81a7564c1d8d33", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fstorage%2Fframework%2Fviews%2Fec711b4f47c4b42ebc81a7564c1d8d33.blade.php&line=1", "ajax": false, "filename": "ec711b4f47c4b42ebc81a7564c1d8d33.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::ec711b4f47c4b42ebc81a7564c1d8d33"}, {"name": "1x core/base::notification.nav-item", "param_count": null, "params": [], "start": **********.342915, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/notification/nav-item.blade.phpcore/base::notification.nav-item", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fnotification%2Fnav-item.blade.php&line=1", "ajax": false, "filename": "nav-item.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::notification.nav-item"}, {"name": "1x __components::306e03d3dc5634ee2e82192f553c6f9b", "param_count": null, "params": [], "start": **********.343802, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\storage\\framework\\views/306e03d3dc5634ee2e82192f553c6f9b.blade.php__components::306e03d3dc5634ee2e82192f553c6f9b", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fstorage%2Fframework%2Fviews%2F306e03d3dc5634ee2e82192f553c6f9b.blade.php&line=1", "ajax": false, "filename": "306e03d3dc5634ee2e82192f553c6f9b.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::306e03d3dc5634ee2e82192f553c6f9b"}, {"name": "1x core/base::layouts.partials.user-menu", "param_count": null, "params": [], "start": **********.347961, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/layouts/partials/user-menu.blade.phpcore/base::layouts.partials.user-menu", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Flayouts%2Fpartials%2Fuser-menu.blade.php&line=1", "ajax": false, "filename": "user-menu.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::layouts.partials.user-menu"}, {"name": "4x ********************************::dropdown.item", "param_count": null, "params": [], "start": **********.380213, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/components/dropdown/item.blade.php********************************::dropdown.item", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fdropdown%2Fitem.blade.php&line=1", "ajax": false, "filename": "item.blade.php", "line": "?"}, "render_count": 4, "name_original": "********************************::dropdown.item"}, {"name": "2x __components::2907df26a6102c24ab0c37391217b338", "param_count": null, "params": [], "start": **********.381625, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\storage\\framework\\views/2907df26a6102c24ab0c37391217b338.blade.php__components::2907df26a6102c24ab0c37391217b338", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fstorage%2Fframework%2Fviews%2F2907df26a6102c24ab0c37391217b338.blade.php&line=1", "ajax": false, "filename": "2907df26a6102c24ab0c37391217b338.blade.php", "line": "?"}, "render_count": 2, "name_original": "__components::2907df26a6102c24ab0c37391217b338"}, {"name": "2x __components::f38bffca7b8a1a50e97a6950ffd66c5c", "param_count": null, "params": [], "start": **********.383091, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\storage\\framework\\views/f38bffca7b8a1a50e97a6950ffd66c5c.blade.php__components::f38bffca7b8a1a50e97a6950ffd66c5c", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fstorage%2Fframework%2Fviews%2Ff38bffca7b8a1a50e97a6950ffd66c5c.blade.php&line=1", "ajax": false, "filename": "f38bffca7b8a1a50e97a6950ffd66c5c.blade.php", "line": "?"}, "render_count": 2, "name_original": "__components::f38bffca7b8a1a50e97a6950ffd66c5c"}, {"name": "2x ********************************::dropdown.index", "param_count": null, "params": [], "start": **********.383379, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/components/dropdown/index.blade.php********************************::dropdown.index", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fdropdown%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 2, "name_original": "********************************::dropdown.index"}, {"name": "1x core/base::layouts.vertical.partials.aside", "param_count": null, "params": [], "start": **********.384155, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/layouts/vertical/partials/aside.blade.phpcore/base::layouts.vertical.partials.aside", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Flayouts%2Fvertical%2Fpartials%2Faside.blade.php&line=1", "ajax": false, "filename": "aside.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::layouts.vertical.partials.aside"}, {"name": "1x core/base::layouts.vertical.partials.sidebar", "param_count": null, "params": [], "start": **********.395725, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/layouts/vertical/partials/sidebar.blade.phpcore/base::layouts.vertical.partials.sidebar", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Flayouts%2Fvertical%2Fpartials%2Fsidebar.blade.php&line=1", "ajax": false, "filename": "sidebar.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::layouts.vertical.partials.sidebar"}, {"name": "1x core/base::layouts.partials.navbar-nav", "param_count": null, "params": [], "start": **********.396386, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/layouts/partials/navbar-nav.blade.phpcore/base::layouts.partials.navbar-nav", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Flayouts%2Fpartials%2Fnavbar-nav.blade.php&line=1", "ajax": false, "filename": "navbar-nav.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::layouts.partials.navbar-nav"}, {"name": "20x core/base::layouts.partials.navbar-nav-item", "param_count": null, "params": [], "start": **********.413482, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/layouts/partials/navbar-nav-item.blade.phpcore/base::layouts.partials.navbar-nav-item", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Flayouts%2Fpartials%2Fnavbar-nav-item.blade.php&line=1", "ajax": false, "filename": "navbar-nav-item.blade.php", "line": "?"}, "render_count": 20, "name_original": "core/base::layouts.partials.navbar-nav-item"}, {"name": "58x core/base::layouts.partials.navbar-nav-item-link", "param_count": null, "params": [], "start": **********.414421, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/layouts/partials/navbar-nav-item-link.blade.phpcore/base::layouts.partials.navbar-nav-item-link", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Flayouts%2Fpartials%2Fnavbar-nav-item-link.blade.php&line=1", "ajax": false, "filename": "navbar-nav-item-link.blade.php", "line": "?"}, "render_count": 58, "name_original": "core/base::layouts.partials.navbar-nav-item-link"}, {"name": "1x __components::af68cda57c5ca67f3b8a7729953880bc", "param_count": null, "params": [], "start": **********.416133, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\storage\\framework\\views/af68cda57c5ca67f3b8a7729953880bc.blade.php__components::af68cda57c5ca67f3b8a7729953880bc", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fstorage%2Fframework%2Fviews%2Faf68cda57c5ca67f3b8a7729953880bc.blade.php&line=1", "ajax": false, "filename": "af68cda57c5ca67f3b8a7729953880bc.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::af68cda57c5ca67f3b8a7729953880bc"}, {"name": "1x __components::5def649a5a47936dda7e47db6ffcfa75", "param_count": null, "params": [], "start": **********.42015, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\storage\\framework\\views/5def649a5a47936dda7e47db6ffcfa75.blade.php__components::5def649a5a47936dda7e47db6ffcfa75", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fstorage%2Fframework%2Fviews%2F5def649a5a47936dda7e47db6ffcfa75.blade.php&line=1", "ajax": false, "filename": "5def649a5a47936dda7e47db6ffcfa75.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::5def649a5a47936dda7e47db6ffcfa75"}, {"name": "18x __components::d890ecc3acbc4ef41a8ece9e81698457", "param_count": null, "params": [], "start": **********.422883, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\storage\\framework\\views/d890ecc3acbc4ef41a8ece9e81698457.blade.php__components::d890ecc3acbc4ef41a8ece9e81698457", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fstorage%2Fframework%2Fviews%2Fd890ecc3acbc4ef41a8ece9e81698457.blade.php&line=1", "ajax": false, "filename": "d890ecc3acbc4ef41a8ece9e81698457.blade.php", "line": "?"}, "render_count": 18, "name_original": "__components::d890ecc3acbc4ef41a8ece9e81698457"}, {"name": "1x __components::dd4c2087b0a47210b5b4e3ee87ef3eca", "param_count": null, "params": [], "start": **********.442075, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\storage\\framework\\views/dd4c2087b0a47210b5b4e3ee87ef3eca.blade.php__components::dd4c2087b0a47210b5b4e3ee87ef3eca", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fstorage%2Fframework%2Fviews%2Fdd4c2087b0a47210b5b4e3ee87ef3eca.blade.php&line=1", "ajax": false, "filename": "dd4c2087b0a47210b5b4e3ee87ef3eca.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::dd4c2087b0a47210b5b4e3ee87ef3eca"}, {"name": "1x __components::998e4178ecae37dacf7321232f455f64", "param_count": null, "params": [], "start": **********.444425, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\storage\\framework\\views/998e4178ecae37dacf7321232f455f64.blade.php__components::998e4178ecae37dacf7321232f455f64", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fstorage%2Fframework%2Fviews%2F998e4178ecae37dacf7321232f455f64.blade.php&line=1", "ajax": false, "filename": "998e4178ecae37dacf7321232f455f64.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::998e4178ecae37dacf7321232f455f64"}, {"name": "1x __components::06e7cfabd119917d6efbd595a344e37b", "param_count": null, "params": [], "start": **********.447546, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\storage\\framework\\views/06e7cfabd119917d6efbd595a344e37b.blade.php__components::06e7cfabd119917d6efbd595a344e37b", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fstorage%2Fframework%2Fviews%2F06e7cfabd119917d6efbd595a344e37b.blade.php&line=1", "ajax": false, "filename": "06e7cfabd119917d6efbd595a344e37b.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::06e7cfabd119917d6efbd595a344e37b"}, {"name": "2x __components::6e0b6ed9bf49c6ad9d02af2c7f911103", "param_count": null, "params": [], "start": **********.450342, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\storage\\framework\\views/6e0b6ed9bf49c6ad9d02af2c7f911103.blade.php__components::6e0b6ed9bf49c6ad9d02af2c7f911103", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fstorage%2Fframework%2Fviews%2F6e0b6ed9bf49c6ad9d02af2c7f911103.blade.php&line=1", "ajax": false, "filename": "6e0b6ed9bf49c6ad9d02af2c7f911103.blade.php", "line": "?"}, "render_count": 2, "name_original": "__components::6e0b6ed9bf49c6ad9d02af2c7f911103"}, {"name": "1x __components::9413e731e9bb6e320e018067130903e9", "param_count": null, "params": [], "start": **********.453278, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\storage\\framework\\views/9413e731e9bb6e320e018067130903e9.blade.php__components::9413e731e9bb6e320e018067130903e9", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fstorage%2Fframework%2Fviews%2F9413e731e9bb6e320e018067130903e9.blade.php&line=1", "ajax": false, "filename": "9413e731e9bb6e320e018067130903e9.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::9413e731e9bb6e320e018067130903e9"}, {"name": "1x __components::ca20cd1247722214b06db9aa7c493b27", "param_count": null, "params": [], "start": **********.456371, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\storage\\framework\\views/ca20cd1247722214b06db9aa7c493b27.blade.php__components::ca20cd1247722214b06db9aa7c493b27", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fstorage%2Fframework%2Fviews%2Fca20cd1247722214b06db9aa7c493b27.blade.php&line=1", "ajax": false, "filename": "ca20cd1247722214b06db9aa7c493b27.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::ca20cd1247722214b06db9aa7c493b27"}, {"name": "4x core/base::partials.navbar.badge-count", "param_count": null, "params": [], "start": **********.457003, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/partials/navbar/badge-count.blade.phpcore/base::partials.navbar.badge-count", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fpartials%2Fnavbar%2Fbadge-count.blade.php&line=1", "ajax": false, "filename": "badge-count.blade.php", "line": "?"}, "render_count": 4, "name_original": "core/base::partials.navbar.badge-count"}, {"name": "4x ********************************::navbar.badge-count", "param_count": null, "params": [], "start": **********.457515, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/components/navbar/badge-count.blade.php********************************::navbar.badge-count", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fnavbar%2Fbadge-count.blade.php&line=1", "ajax": false, "filename": "badge-count.blade.php", "line": "?"}, "render_count": 4, "name_original": "********************************::navbar.badge-count"}, {"name": "1x __components::a3acd3bd206d0793e18d491720de886c", "param_count": null, "params": [], "start": **********.465192, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\storage\\framework\\views/a3acd3bd206d0793e18d491720de886c.blade.php__components::a3acd3bd206d0793e18d491720de886c", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fstorage%2Fframework%2Fviews%2Fa3acd3bd206d0793e18d491720de886c.blade.php&line=1", "ajax": false, "filename": "a3acd3bd206d0793e18d491720de886c.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::a3acd3bd206d0793e18d491720de886c"}, {"name": "1x __components::ff9949c19e885406625d67d91b780159", "param_count": null, "params": [], "start": **********.468313, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\storage\\framework\\views/ff9949c19e885406625d67d91b780159.blade.php__components::ff9949c19e885406625d67d91b780159", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fstorage%2Fframework%2Fviews%2Fff9949c19e885406625d67d91b780159.blade.php&line=1", "ajax": false, "filename": "ff9949c19e885406625d67d91b780159.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::ff9949c19e885406625d67d91b780159"}, {"name": "1x __components::fd978891e1ac33723cbffddc6658659a", "param_count": null, "params": [], "start": **********.474245, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\storage\\framework\\views/fd978891e1ac33723cbffddc6658659a.blade.php__components::fd978891e1ac33723cbffddc6658659a", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fstorage%2Fframework%2Fviews%2Ffd978891e1ac33723cbffddc6658659a.blade.php&line=1", "ajax": false, "filename": "fd978891e1ac33723cbffddc6658659a.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::fd978891e1ac33723cbffddc6658659a"}, {"name": "1x __components::bbf20ce5b72ea2c57e1a89503478967d", "param_count": null, "params": [], "start": **********.480096, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\storage\\framework\\views/bbf20ce5b72ea2c57e1a89503478967d.blade.php__components::bbf20ce5b72ea2c57e1a89503478967d", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fstorage%2Fframework%2Fviews%2Fbbf20ce5b72ea2c57e1a89503478967d.blade.php&line=1", "ajax": false, "filename": "bbf20ce5b72ea2c57e1a89503478967d.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::bbf20ce5b72ea2c57e1a89503478967d"}, {"name": "1x __components::13365b7e5a448d13150fdb4b3884b510", "param_count": null, "params": [], "start": **********.483456, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\storage\\framework\\views/13365b7e5a448d13150fdb4b3884b510.blade.php__components::13365b7e5a448d13150fdb4b3884b510", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fstorage%2Fframework%2Fviews%2F13365b7e5a448d13150fdb4b3884b510.blade.php&line=1", "ajax": false, "filename": "13365b7e5a448d13150fdb4b3884b510.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::13365b7e5a448d13150fdb4b3884b510"}, {"name": "2x __components::98e88d58787b8dfeb6f0d1dc0a785cfd", "param_count": null, "params": [], "start": **********.486346, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\storage\\framework\\views/98e88d58787b8dfeb6f0d1dc0a785cfd.blade.php__components::98e88d58787b8dfeb6f0d1dc0a785cfd", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fstorage%2Fframework%2Fviews%2F98e88d58787b8dfeb6f0d1dc0a785cfd.blade.php&line=1", "ajax": false, "filename": "98e88d58787b8dfeb6f0d1dc0a785cfd.blade.php", "line": "?"}, "render_count": 2, "name_original": "__components::98e88d58787b8dfeb6f0d1dc0a785cfd"}, {"name": "1x __components::cbce7a4c13e13a70eabb7759894a60fd", "param_count": null, "params": [], "start": **********.48921, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\storage\\framework\\views/cbce7a4c13e13a70eabb7759894a60fd.blade.php__components::cbce7a4c13e13a70eabb7759894a60fd", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fstorage%2Fframework%2Fviews%2Fcbce7a4c13e13a70eabb7759894a60fd.blade.php&line=1", "ajax": false, "filename": "cbce7a4c13e13a70eabb7759894a60fd.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::cbce7a4c13e13a70eabb7759894a60fd"}, {"name": "1x __components::fe6fcb7551f99a7d9d1c5a4c0011f471", "param_count": null, "params": [], "start": **********.491301, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\storage\\framework\\views/fe6fcb7551f99a7d9d1c5a4c0011f471.blade.php__components::fe6fcb7551f99a7d9d1c5a4c0011f471", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fstorage%2Fframework%2Fviews%2Ffe6fcb7551f99a7d9d1c5a4c0011f471.blade.php&line=1", "ajax": false, "filename": "fe6fcb7551f99a7d9d1c5a4c0011f471.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::fe6fcb7551f99a7d9d1c5a4c0011f471"}, {"name": "1x __components::351bdfbe842eff22e08f1df9d5f5beb1", "param_count": null, "params": [], "start": **********.496198, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\storage\\framework\\views/351bdfbe842eff22e08f1df9d5f5beb1.blade.php__components::351bdfbe842eff22e08f1df9d5f5beb1", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fstorage%2Fframework%2Fviews%2F351bdfbe842eff22e08f1df9d5f5beb1.blade.php&line=1", "ajax": false, "filename": "351bdfbe842eff22e08f1df9d5f5beb1.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::351bdfbe842eff22e08f1df9d5f5beb1"}, {"name": "1x __components::0343a1b0800146d7d9cf6a9514ec7bf4", "param_count": null, "params": [], "start": **********.505019, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\storage\\framework\\views/0343a1b0800146d7d9cf6a9514ec7bf4.blade.php__components::0343a1b0800146d7d9cf6a9514ec7bf4", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fstorage%2Fframework%2Fviews%2F0343a1b0800146d7d9cf6a9514ec7bf4.blade.php&line=1", "ajax": false, "filename": "0343a1b0800146d7d9cf6a9514ec7bf4.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::0343a1b0800146d7d9cf6a9514ec7bf4"}, {"name": "1x __components::15422be7d0f2aaf8c8244c1e9db20ad9", "param_count": null, "params": [], "start": **********.50733, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\storage\\framework\\views/15422be7d0f2aaf8c8244c1e9db20ad9.blade.php__components::15422be7d0f2aaf8c8244c1e9db20ad9", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fstorage%2Fframework%2Fviews%2F15422be7d0f2aaf8c8244c1e9db20ad9.blade.php&line=1", "ajax": false, "filename": "15422be7d0f2aaf8c8244c1e9db20ad9.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::15422be7d0f2aaf8c8244c1e9db20ad9"}, {"name": "1x __components::2e5e965add6d0ee3aadb02ca38e70825", "param_count": null, "params": [], "start": **********.509628, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\storage\\framework\\views/2e5e965add6d0ee3aadb02ca38e70825.blade.php__components::2e5e965add6d0ee3aadb02ca38e70825", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fstorage%2Fframework%2Fviews%2F2e5e965add6d0ee3aadb02ca38e70825.blade.php&line=1", "ajax": false, "filename": "2e5e965add6d0ee3aadb02ca38e70825.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::2e5e965add6d0ee3aadb02ca38e70825"}, {"name": "1x __components::5b8d99843e0f8eff6046d7236026b187", "param_count": null, "params": [], "start": **********.512504, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\storage\\framework\\views/5b8d99843e0f8eff6046d7236026b187.blade.php__components::5b8d99843e0f8eff6046d7236026b187", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fstorage%2Fframework%2Fviews%2F5b8d99843e0f8eff6046d7236026b187.blade.php&line=1", "ajax": false, "filename": "5b8d99843e0f8eff6046d7236026b187.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::5b8d99843e0f8eff6046d7236026b187"}, {"name": "1x __components::dd248739db8ba923ebfe1f426bb71a38", "param_count": null, "params": [], "start": **********.516105, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\storage\\framework\\views/dd248739db8ba923ebfe1f426bb71a38.blade.php__components::dd248739db8ba923ebfe1f426bb71a38", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fstorage%2Fframework%2Fviews%2Fdd248739db8ba923ebfe1f426bb71a38.blade.php&line=1", "ajax": false, "filename": "dd248739db8ba923ebfe1f426bb71a38.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::dd248739db8ba923ebfe1f426bb71a38"}, {"name": "2x __components::1d6f928aaf1e585d3246cb3bee8fdd45", "param_count": null, "params": [], "start": **********.51984, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\storage\\framework\\views/1d6f928aaf1e585d3246cb3bee8fdd45.blade.php__components::1d6f928aaf1e585d3246cb3bee8fdd45", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fstorage%2Fframework%2Fviews%2F1d6f928aaf1e585d3246cb3bee8fdd45.blade.php&line=1", "ajax": false, "filename": "1d6f928aaf1e585d3246cb3bee8fdd45.blade.php", "line": "?"}, "render_count": 2, "name_original": "__components::1d6f928aaf1e585d3246cb3bee8fdd45"}, {"name": "1x __components::acbc3e3e19c70a7ebb0e7a5d98d84fbe", "param_count": null, "params": [], "start": **********.523558, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\storage\\framework\\views/acbc3e3e19c70a7ebb0e7a5d98d84fbe.blade.php__components::acbc3e3e19c70a7ebb0e7a5d98d84fbe", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fstorage%2Fframework%2Fviews%2Facbc3e3e19c70a7ebb0e7a5d98d84fbe.blade.php&line=1", "ajax": false, "filename": "acbc3e3e19c70a7ebb0e7a5d98d84fbe.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::acbc3e3e19c70a7ebb0e7a5d98d84fbe"}, {"name": "1x __components::dac323985d9d2618ad252313442aaf03", "param_count": null, "params": [], "start": **********.530029, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\storage\\framework\\views/dac323985d9d2618ad252313442aaf03.blade.php__components::dac323985d9d2618ad252313442aaf03", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fstorage%2Fframework%2Fviews%2Fdac323985d9d2618ad252313442aaf03.blade.php&line=1", "ajax": false, "filename": "dac323985d9d2618ad252313442aaf03.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::dac323985d9d2618ad252313442aaf03"}, {"name": "1x __components::298cd8a12b86a6f371ff06491a0822fa", "param_count": null, "params": [], "start": **********.532197, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\storage\\framework\\views/298cd8a12b86a6f371ff06491a0822fa.blade.php__components::298cd8a12b86a6f371ff06491a0822fa", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fstorage%2Fframework%2Fviews%2F298cd8a12b86a6f371ff06491a0822fa.blade.php&line=1", "ajax": false, "filename": "298cd8a12b86a6f371ff06491a0822fa.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::298cd8a12b86a6f371ff06491a0822fa"}, {"name": "1x __components::c59870a61b233f0766e3260625bdb025", "param_count": null, "params": [], "start": **********.534974, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\storage\\framework\\views/c59870a61b233f0766e3260625bdb025.blade.php__components::c59870a61b233f0766e3260625bdb025", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fstorage%2Fframework%2Fviews%2Fc59870a61b233f0766e3260625bdb025.blade.php&line=1", "ajax": false, "filename": "c59870a61b233f0766e3260625bdb025.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::c59870a61b233f0766e3260625bdb025"}, {"name": "1x __components::3890eca5d46a147ef99ddf994453d2ec", "param_count": null, "params": [], "start": **********.538362, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\storage\\framework\\views/3890eca5d46a147ef99ddf994453d2ec.blade.php__components::3890eca5d46a147ef99ddf994453d2ec", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fstorage%2Fframework%2Fviews%2F3890eca5d46a147ef99ddf994453d2ec.blade.php&line=1", "ajax": false, "filename": "3890eca5d46a147ef99ddf994453d2ec.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::3890eca5d46a147ef99ddf994453d2ec"}, {"name": "1x __components::ff1fde71531b073b2c658173537aaea5", "param_count": null, "params": [], "start": **********.541256, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\storage\\framework\\views/ff1fde71531b073b2c658173537aaea5.blade.php__components::ff1fde71531b073b2c658173537aaea5", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fstorage%2Fframework%2Fviews%2Fff1fde71531b073b2c658173537aaea5.blade.php&line=1", "ajax": false, "filename": "ff1fde71531b073b2c658173537aaea5.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::ff1fde71531b073b2c658173537aaea5"}, {"name": "1x __components::5cef0de51e1489c31c7fcb5d7f2f6a97", "param_count": null, "params": [], "start": **********.544403, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\storage\\framework\\views/5cef0de51e1489c31c7fcb5d7f2f6a97.blade.php__components::5cef0de51e1489c31c7fcb5d7f2f6a97", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fstorage%2Fframework%2Fviews%2F5cef0de51e1489c31c7fcb5d7f2f6a97.blade.php&line=1", "ajax": false, "filename": "5cef0de51e1489c31c7fcb5d7f2f6a97.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::5cef0de51e1489c31c7fcb5d7f2f6a97"}, {"name": "1x __components::89cb89d3fdb0a0a12f8aa61073c231e6", "param_count": null, "params": [], "start": **********.547256, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\storage\\framework\\views/89cb89d3fdb0a0a12f8aa61073c231e6.blade.php__components::89cb89d3fdb0a0a12f8aa61073c231e6", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fstorage%2Fframework%2Fviews%2F89cb89d3fdb0a0a12f8aa61073c231e6.blade.php&line=1", "ajax": false, "filename": "89cb89d3fdb0a0a12f8aa61073c231e6.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::89cb89d3fdb0a0a12f8aa61073c231e6"}, {"name": "1x __components::5a5b09d3f2ee0ddb2b536feb532d230a", "param_count": null, "params": [], "start": **********.550005, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\storage\\framework\\views/5a5b09d3f2ee0ddb2b536feb532d230a.blade.php__components::5a5b09d3f2ee0ddb2b536feb532d230a", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fstorage%2Fframework%2Fviews%2F5a5b09d3f2ee0ddb2b536feb532d230a.blade.php&line=1", "ajax": false, "filename": "5a5b09d3f2ee0ddb2b536feb532d230a.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::5a5b09d3f2ee0ddb2b536feb532d230a"}, {"name": "1x __components::fedc652debeb23dcbb31a98830baa397", "param_count": null, "params": [], "start": **********.552449, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\storage\\framework\\views/fedc652debeb23dcbb31a98830baa397.blade.php__components::fedc652debeb23dcbb31a98830baa397", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fstorage%2Fframework%2Fviews%2Ffedc652debeb23dcbb31a98830baa397.blade.php&line=1", "ajax": false, "filename": "fedc652debeb23dcbb31a98830baa397.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::fedc652debeb23dcbb31a98830baa397"}, {"name": "1x __components::ff3b2cf4e42e74e63db76ff05c5f2374", "param_count": null, "params": [], "start": **********.555542, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\storage\\framework\\views/ff3b2cf4e42e74e63db76ff05c5f2374.blade.php__components::ff3b2cf4e42e74e63db76ff05c5f2374", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fstorage%2Fframework%2Fviews%2Fff3b2cf4e42e74e63db76ff05c5f2374.blade.php&line=1", "ajax": false, "filename": "ff3b2cf4e42e74e63db76ff05c5f2374.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::ff3b2cf4e42e74e63db76ff05c5f2374"}, {"name": "1x __components::745871da7c635a3f461dfaeeef54a48e", "param_count": null, "params": [], "start": **********.557949, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\storage\\framework\\views/745871da7c635a3f461dfaeeef54a48e.blade.php__components::745871da7c635a3f461dfaeeef54a48e", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fstorage%2Fframework%2Fviews%2F745871da7c635a3f461dfaeeef54a48e.blade.php&line=1", "ajax": false, "filename": "745871da7c635a3f461dfaeeef54a48e.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::745871da7c635a3f461dfaeeef54a48e"}, {"name": "1x __components::2b3233eda7e50501ef45fd875b12da49", "param_count": null, "params": [], "start": **********.565041, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\storage\\framework\\views/2b3233eda7e50501ef45fd875b12da49.blade.php__components::2b3233eda7e50501ef45fd875b12da49", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fstorage%2Fframework%2Fviews%2F2b3233eda7e50501ef45fd875b12da49.blade.php&line=1", "ajax": false, "filename": "2b3233eda7e50501ef45fd875b12da49.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::2b3233eda7e50501ef45fd875b12da49"}, {"name": "1x __components::b13663c834a4ae876ef8f72aa0610e8c", "param_count": null, "params": [], "start": **********.568493, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\storage\\framework\\views/b13663c834a4ae876ef8f72aa0610e8c.blade.php__components::b13663c834a4ae876ef8f72aa0610e8c", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fstorage%2Fframework%2Fviews%2Fb13663c834a4ae876ef8f72aa0610e8c.blade.php&line=1", "ajax": false, "filename": "b13663c834a4ae876ef8f72aa0610e8c.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::b13663c834a4ae876ef8f72aa0610e8c"}, {"name": "1x core/base::layouts.partials.page-header", "param_count": null, "params": [], "start": **********.56939, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/layouts/partials/page-header.blade.phpcore/base::layouts.partials.page-header", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Flayouts%2Fpartials%2Fpage-header.blade.php&line=1", "ajax": false, "filename": "page-header.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::layouts.partials.page-header"}, {"name": "1x core/base::breadcrumb", "param_count": null, "params": [], "start": **********.569882, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/breadcrumb.blade.phpcore/base::breadcrumb", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fbreadcrumb.blade.php&line=1", "ajax": false, "filename": "breadcrumb.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::breadcrumb"}, {"name": "1x core/base::layouts.partials.footer", "param_count": null, "params": [], "start": **********.570856, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/layouts/partials/footer.blade.phpcore/base::layouts.partials.footer", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Flayouts%2Fpartials%2Ffooter.blade.php&line=1", "ajax": false, "filename": "footer.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::layouts.partials.footer"}, {"name": "1x core/base::partials.copyright", "param_count": null, "params": [], "start": **********.571319, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/partials/copyright.blade.phpcore/base::partials.copyright", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fpartials%2Fcopyright.blade.php&line=1", "ajax": false, "filename": "copyright.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::partials.copyright"}, {"name": "1x core/base::layouts.vertical.partials.after-content", "param_count": null, "params": [], "start": **********.572496, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/layouts/vertical/partials/after-content.blade.phpcore/base::layouts.vertical.partials.after-content", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Flayouts%2Fvertical%2Fpartials%2Fafter-content.blade.php&line=1", "ajax": false, "filename": "after-content.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::layouts.vertical.partials.after-content"}, {"name": "1x core/base::global-search.form", "param_count": null, "params": [], "start": **********.573144, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/global-search/form.blade.phpcore/base::global-search.form", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fglobal-search%2Fform.blade.php&line=1", "ajax": false, "filename": "form.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::global-search.form"}, {"name": "1x __components::3cec1c87224222bda738c53f782c5bc1", "param_count": null, "params": [], "start": **********.575004, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\storage\\framework\\views/3cec1c87224222bda738c53f782c5bc1.blade.php__components::3cec1c87224222bda738c53f782c5bc1", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fstorage%2Fframework%2Fviews%2F3cec1c87224222bda738c53f782c5bc1.blade.php&line=1", "ajax": false, "filename": "3cec1c87224222bda738c53f782c5bc1.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::3cec1c87224222bda738c53f782c5bc1"}, {"name": "1x ********************************::form.index", "param_count": null, "params": [], "start": **********.57729, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/components/form/index.blade.php********************************::form.index", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fform%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "********************************::form.index"}, {"name": "1x __components::414e4e803eeec6389552bb46515583c5", "param_count": null, "params": [], "start": **********.578323, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\storage\\framework\\views/414e4e803eeec6389552bb46515583c5.blade.php__components::414e4e803eeec6389552bb46515583c5", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fstorage%2Fframework%2Fviews%2F414e4e803eeec6389552bb46515583c5.blade.php&line=1", "ajax": false, "filename": "414e4e803eeec6389552bb46515583c5.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::414e4e803eeec6389552bb46515583c5"}, {"name": "1x __components::c47a448d99d5719cb034f7947c739ff8", "param_count": null, "params": [], "start": **********.579286, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\storage\\framework\\views/c47a448d99d5719cb034f7947c739ff8.blade.php__components::c47a448d99d5719cb034f7947c739ff8", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fstorage%2Fframework%2Fviews%2Fc47a448d99d5719cb034f7947c739ff8.blade.php&line=1", "ajax": false, "filename": "c47a448d99d5719cb034f7947c739ff8.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::c47a448d99d5719cb034f7947c739ff8"}, {"name": "1x __components::e226165e1fca7eccb10f6857d7cd235a", "param_count": null, "params": [], "start": **********.580292, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\storage\\framework\\views/e226165e1fca7eccb10f6857d7cd235a.blade.php__components::e226165e1fca7eccb10f6857d7cd235a", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fstorage%2Fframework%2Fviews%2Fe226165e1fca7eccb10f6857d7cd235a.blade.php&line=1", "ajax": false, "filename": "e226165e1fca7eccb10f6857d7cd235a.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::e226165e1fca7eccb10f6857d7cd235a"}, {"name": "4x ********************************::modal", "param_count": null, "params": [], "start": **********.580689, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/components/modal.blade.php********************************::modal", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fmodal.blade.php&line=1", "ajax": false, "filename": "modal.blade.php", "line": "?"}, "render_count": 4, "name_original": "********************************::modal"}, {"name": "1x ********************************::custom-template", "param_count": null, "params": [], "start": **********.581894, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/components/custom-template.blade.php********************************::custom-template", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fcustom-template.blade.php&line=1", "ajax": false, "filename": "custom-template.blade.php", "line": "?"}, "render_count": 1, "name_original": "********************************::custom-template"}, {"name": "1x core/media::partials.media", "param_count": null, "params": [], "start": **********.58278, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/media/resources/views/partials/media.blade.phpcore/media::partials.media", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fmedia%2Fresources%2Fviews%2Fpartials%2Fmedia.blade.php&line=1", "ajax": false, "filename": "media.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/media::partials.media"}, {"name": "4x ********************************::modal.close-button", "param_count": null, "params": [], "start": **********.584468, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/components/modal/close-button.blade.php********************************::modal.close-button", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fmodal%2Fclose-button.blade.php&line=1", "ajax": false, "filename": "close-button.blade.php", "line": "?"}, "render_count": 4, "name_original": "********************************::modal.close-button"}, {"name": "1x ********************************::loading", "param_count": null, "params": [], "start": **********.585075, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/components/loading.blade.php********************************::loading", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Floading.blade.php&line=1", "ajax": false, "filename": "loading.blade.php", "line": "?"}, "render_count": 1, "name_original": "********************************::loading"}, {"name": "1x ********************************::form.checkbox", "param_count": null, "params": [], "start": **********.588948, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/components/form/checkbox.blade.php********************************::form.checkbox", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fform%2Fcheckbox.blade.php&line=1", "ajax": false, "filename": "checkbox.blade.php", "line": "?"}, "render_count": 1, "name_original": "********************************::form.checkbox"}, {"name": "1x core/media::config", "param_count": null, "params": [], "start": **********.595309, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/media/resources/views/config.blade.phpcore/media::config", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fmedia%2Fresources%2Fviews%2Fconfig.blade.php&line=1", "ajax": false, "filename": "config.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/media::config"}, {"name": "1x ********************************::debug-badge", "param_count": null, "params": [], "start": **********.764493, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/components/debug-badge.blade.php********************************::debug-badge", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fdebug-badge.blade.php&line=1", "ajax": false, "filename": "debug-badge.blade.php", "line": "?"}, "render_count": 1, "name_original": "********************************::debug-badge"}, {"name": "2x ********************************::modal.action", "param_count": null, "params": [], "start": **********.765779, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/components/modal/action.blade.php********************************::modal.action", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fmodal%2Faction.blade.php&line=1", "ajax": false, "filename": "action.blade.php", "line": "?"}, "render_count": 2, "name_original": "********************************::modal.action"}, {"name": "2x ********************************::modal.alert", "param_count": null, "params": [], "start": **********.767212, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/components/modal/alert.blade.php********************************::modal.alert", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fmodal%2Falert.blade.php&line=1", "ajax": false, "filename": "alert.blade.php", "line": "?"}, "render_count": 2, "name_original": "********************************::modal.alert"}, {"name": "1x __components::dcf17957c4aa053a618fd9c312cc29fc", "param_count": null, "params": [], "start": **********.769416, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\storage\\framework\\views/dcf17957c4aa053a618fd9c312cc29fc.blade.php__components::dcf17957c4aa053a618fd9c312cc29fc", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fstorage%2Fframework%2Fviews%2Fdcf17957c4aa053a618fd9c312cc29fc.blade.php&line=1", "ajax": false, "filename": "dcf17957c4aa053a618fd9c312cc29fc.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::dcf17957c4aa053a618fd9c312cc29fc"}, {"name": "1x __components::fcfb9f9ba5fb460899f38b71f491e1fe", "param_count": null, "params": [], "start": **********.77451, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\storage\\framework\\views/fcfb9f9ba5fb460899f38b71f491e1fe.blade.php__components::fcfb9f9ba5fb460899f38b71f491e1fe", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fstorage%2Fframework%2Fviews%2Ffcfb9f9ba5fb460899f38b71f491e1fe.blade.php&line=1", "ajax": false, "filename": "fcfb9f9ba5fb460899f38b71f491e1fe.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::fcfb9f9ba5fb460899f38b71f491e1fe"}, {"name": "1x ********************************::layouts.base", "param_count": null, "params": [], "start": **********.776192, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/components/layouts/base.blade.php********************************::layouts.base", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Flayouts%2Fbase.blade.php&line=1", "ajax": false, "filename": "base.blade.php", "line": "?"}, "render_count": 1, "name_original": "********************************::layouts.base"}, {"name": "1x core/base::components.layouts.header", "param_count": null, "params": [], "start": **********.777463, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/components/layouts/header.blade.phpcore/base::components.layouts.header", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Flayouts%2Fheader.blade.php&line=1", "ajax": false, "filename": "header.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::components.layouts.header"}, {"name": "1x assets::header", "param_count": null, "params": [], "start": **********.791487, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform\\packages\\laravel-assets\\src\\Providers/../../resources/views/header.blade.phpassets::header", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fpackages%2Flaravel-assets%2Fresources%2Fviews%2Fheader.blade.php&line=1", "ajax": false, "filename": "header.blade.php", "line": "?"}, "render_count": 1, "name_original": "assets::header"}, {"name": "1x core/base::elements.common", "param_count": null, "params": [], "start": **********.79344, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/elements/common.blade.phpcore/base::elements.common", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Felements%2Fcommon.blade.php&line=1", "ajax": false, "filename": "common.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::elements.common"}, {"name": "1x assets::footer", "param_count": null, "params": [], "start": **********.79589, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform\\packages\\laravel-assets\\src\\Providers/../../resources/views/footer.blade.phpassets::footer", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fpackages%2Flaravel-assets%2Fresources%2Fviews%2Ffooter.blade.php&line=1", "ajax": false, "filename": "footer.blade.php", "line": "?"}, "render_count": 1, "name_original": "assets::footer"}, {"name": "1x core/base::notification.notification", "param_count": null, "params": [], "start": **********.796924, "type": "blade", "hash": "bladeD:\\laragon\\www\\xmetr\\platform/core/base/resources/views/notification/notification.blade.phpcore/base::notification.notification", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fnotification%2Fnotification.blade.php&line=1", "ajax": false, "filename": "notification.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::notification.notification"}]}, "queries": {"count": 12, "nb_statements": 12, "nb_visible_statements": 12, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.00563, "accumulated_duration_str": "5.63ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 76}], "start": **********.104204, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 0, "width_percent": 7.105}, {"sql": "select * from `districts` where `id` = '1' limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ImplicitRouteBinding.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ImplicitRouteBinding.php", "line": 61}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 959}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 41}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.107384, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 7.105, "width_percent": 7.105}, {"sql": "select `name`, `id` from `countries` where `status` = 'published' order by `order` asc, `name` asc", "type": "query", "params": [], "bindings": ["published"], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "platform/plugins/location/src/Forms/DistrictForm.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\location\\src\\Forms\\DistrictForm.php", "line": 32}, {"index": 15, "namespace": null, "name": "platform/core/base/src/Forms/FormAbstract.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Forms\\FormAbstract.php", "line": 98}, {"index": 16, "namespace": null, "name": "platform/packages/form-builder/src/FormBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\form-builder\\src\\FormBuilder.php", "line": 44}, {"index": 17, "namespace": null, "name": "platform/core/base/src/Forms/FormBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Forms\\FormBuilder.php", "line": 11}, {"index": 18, "namespace": null, "name": "platform/core/base/src/Forms/FormAbstract.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Forms\\FormAbstract.php", "line": 570}], "start": **********.118665, "duration": 0.00067, "duration_str": "670μs", "memory": 0, "memory_str": null, "filename": "DistrictForm.php:32", "source": {"index": 14, "namespace": null, "name": "platform/plugins/location/src/Forms/DistrictForm.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\location\\src\\Forms\\DistrictForm.php", "line": 32}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Flocation%2Fsrc%2FForms%2FDistrictForm.php&line=32", "ajax": false, "filename": "DistrictForm.php", "line": "32"}, "connection": "xmetr", "explain": null, "start_percent": 14.21, "width_percent": 11.901}, {"sql": "select `name`, `id` from `states` where `country_id` = 22 and `status` = 'published' order by `order` asc, `name` asc", "type": "query", "params": [], "bindings": [22, "published"], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "platform/plugins/location/src/Forms/DistrictForm.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\location\\src\\Forms\\DistrictForm.php", "line": 44}, {"index": 15, "namespace": null, "name": "platform/core/base/src/Forms/FormAbstract.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Forms\\FormAbstract.php", "line": 98}, {"index": 16, "namespace": null, "name": "platform/packages/form-builder/src/FormBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\form-builder\\src\\FormBuilder.php", "line": 44}, {"index": 17, "namespace": null, "name": "platform/core/base/src/Forms/FormBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Forms\\FormBuilder.php", "line": 11}, {"index": 18, "namespace": null, "name": "platform/core/base/src/Forms/FormAbstract.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Forms\\FormAbstract.php", "line": 570}], "start": **********.125949, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "DistrictForm.php:44", "source": {"index": 14, "namespace": null, "name": "platform/plugins/location/src/Forms/DistrictForm.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\location\\src\\Forms\\DistrictForm.php", "line": 44}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Flocation%2Fsrc%2FForms%2FDistrictForm.php&line=44", "ajax": false, "filename": "DistrictForm.php", "line": "44"}, "connection": "xmetr", "explain": null, "start_percent": 26.11, "width_percent": 9.591}, {"sql": "select `name`, `id` from `cities` where `state_id` = 187 and `status` = 'published' order by `order` asc, `name` asc", "type": "query", "params": [], "bindings": [187, "published"], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "platform/plugins/location/src/Forms/DistrictForm.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\location\\src\\Forms\\DistrictForm.php", "line": 53}, {"index": 15, "namespace": null, "name": "platform/core/base/src/Forms/FormAbstract.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Forms\\FormAbstract.php", "line": 98}, {"index": 16, "namespace": null, "name": "platform/packages/form-builder/src/FormBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\form-builder\\src\\FormBuilder.php", "line": 44}, {"index": 17, "namespace": null, "name": "platform/core/base/src/Forms/FormBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Forms\\FormBuilder.php", "line": 11}, {"index": 18, "namespace": null, "name": "platform/core/base/src/Forms/FormAbstract.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Forms\\FormAbstract.php", "line": 570}], "start": **********.127663, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "DistrictForm.php:53", "source": {"index": 14, "namespace": null, "name": "platform/plugins/location/src/Forms/DistrictForm.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\location\\src\\Forms\\DistrictForm.php", "line": 53}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Flocation%2Fsrc%2FForms%2FDistrictForm.php&line=53", "ajax": false, "filename": "DistrictForm.php", "line": "53"}, "connection": "xmetr", "explain": null, "start_percent": 35.702, "width_percent": 7.46}, {"sql": "select `id`, `key`, `reference_type`, `reference_id`, `prefix` from `slugs` where `slugs`.`reference_type` = 'Xmetr\\\\Location\\\\Models\\\\District' and `slugs`.`reference_id` = 1 and `slugs`.`reference_id` is not null limit 1", "type": "query", "params": [], "bindings": ["Xmetr\\Location\\Models\\District", 1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 23, "namespace": null, "name": "platform/packages/slug/src/Providers/SlugServiceProvider.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\slug\\src\\Providers\\SlugServiceProvider.php", "line": 95}, {"index": 29, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 25}, {"index": 32, "namespace": null, "name": "platform/packages/form-builder/src/Fields/FormField.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\form-builder\\src\\Fields\\FormField.php", "line": 521}], "start": **********.149935, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 43.162, "width_percent": 8.171}, {"sql": "select `lang_name` from `languages` where `lang_code` = 'en_US' limit 1", "type": "query", "params": [], "bindings": ["en_US"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 18, "namespace": null, "name": "platform/plugins/language-advanced/src/Providers/HookServiceProvider.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\language-advanced\\src\\Providers\\HookServiceProvider.php", "line": 163}, {"index": 22, "namespace": null, "name": "platform/core/base/helpers/action-filter.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\helpers\\action-filter.php", "line": 46}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.156673, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 51.332, "width_percent": 8.703}, {"sql": "select * from `meta_boxes` where (`meta_key` = 'seo_meta' and `reference_id` = 1 and `reference_type` = 'Xmetr\\\\Location\\\\Models\\\\District') limit 1", "type": "query", "params": [], "bindings": ["seo_meta", 1, "Xmetr\\Location\\Models\\District"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/core/base/src/Supports/MetaBox.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Supports\\MetaBox.php", "line": 186}, {"index": 18, "namespace": null, "name": "platform/core/base/src/Supports/MetaBox.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Supports\\MetaBox.php", "line": 168}, {"index": 20, "namespace": null, "name": "platform/packages/seo-helper/src/Providers/HookServiceProvider.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\seo-helper\\src\\Providers\\HookServiceProvider.php", "line": 65}, {"index": 27, "namespace": null, "name": "platform/core/base/helpers/action-filter.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\helpers\\action-filter.php", "line": 46}], "start": **********.2047231, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 60.036, "width_percent": 8.348}, {"sql": "select `lang_code`, `lang_flag`, `lang_name` from `languages` order by `lang_order` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 16, "namespace": null, "name": "platform/plugins/language/src/LanguageManager.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\language\\src\\LanguageManager.php", "line": 156}, {"index": 18, "namespace": null, "name": "platform/plugins/language-advanced/src/Providers/HookServiceProvider.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\language-advanced\\src\\Providers\\HookServiceProvider.php", "line": 69}, {"index": 22, "namespace": null, "name": "platform/core/base/helpers/action-filter.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\helpers\\action-filter.php", "line": 46}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.2558372, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 68.384, "width_percent": 7.815}, {"sql": "select `lang_flag`, `lang_name`, `lang_code` from `languages` where `lang_is_default` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/language/src/LanguageManager.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\language\\src\\LanguageManager.php", "line": 938}, {"index": 19, "namespace": null, "name": "platform/plugins/language-advanced/src/Providers/HookServiceProvider.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\language-advanced\\src\\Providers\\HookServiceProvider.php", "line": 102}, {"index": 26, "namespace": null, "name": "platform/core/base/helpers/action-filter.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\helpers\\action-filter.php", "line": 46}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.258518, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 76.199, "width_percent": 7.46}, {"sql": "select * from `languages` order by `lang_order` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 16, "namespace": null, "name": "platform/plugins/language/src/LanguageManager.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\language\\src\\LanguageManager.php", "line": 156}, {"index": 18, "namespace": null, "name": "platform/plugins/language/src/Providers/HookServiceProvider.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\language\\src\\Providers\\HookServiceProvider.php", "line": 81}, {"index": 22, "namespace": null, "name": "platform/core/base/helpers/action-filter.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\helpers\\action-filter.php", "line": 39}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.266361, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "xmetr", "explain": null, "start_percent": 83.659, "width_percent": 8.881}, {"sql": "select count(*) as aggregate from `re_consults` where `status` = 'unread'", "type": "query", "params": [], "bindings": ["unread"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "platform/plugins/real-estate/src/Providers/HookServiceProvider.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Providers\\HookServiceProvider.php", "line": 721}, {"index": 20, "namespace": null, "name": "platform/core/base/helpers/action-filter.php", "file": "D:\\laragon\\www\\xmetr\\platform\\core\\base\\helpers\\action-filter.php", "line": 39}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": **********.346489, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "HookServiceProvider.php:721", "source": {"index": 16, "namespace": null, "name": "platform/plugins/real-estate/src/Providers/HookServiceProvider.php", "file": "D:\\laragon\\www\\xmetr\\platform\\plugins\\real-estate\\src\\Providers\\HookServiceProvider.php", "line": 721}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Freal-estate%2Fsrc%2FProviders%2FHookServiceProvider.php&line=721", "ajax": false, "filename": "HookServiceProvider.php", "line": "721"}, "connection": "xmetr", "explain": null, "start_percent": 92.54, "width_percent": 7.46}]}, "models": {"data": {"Xmetr\\Language\\Models\\Language": {"value": 12, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Flanguage%2Fsrc%2FModels%2FLanguage.php&line=1", "ajax": false, "filename": "Language.php", "line": "?"}}, "Xmetr\\Location\\Models\\Country": {"value": 10, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Flocation%2Fsrc%2FModels%2FCountry.php&line=1", "ajax": false, "filename": "Country.php", "line": "?"}}, "Xmetr\\ACL\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Facl%2Fsrc%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Xmetr\\Location\\Models\\District": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Flocation%2Fsrc%2FModels%2FDistrict.php&line=1", "ajax": false, "filename": "District.php", "line": "?"}}, "Xmetr\\Slug\\Models\\Slug": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fpackages%2Fslug%2Fsrc%2FModels%2FSlug.php&line=1", "ajax": false, "filename": "Slug.php", "line": "?"}}, "Xmetr\\Base\\Models\\MetaBox": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FMetaBox.php&line=1", "ajax": false, "filename": "MetaBox.php", "line": "?"}}}, "count": 26, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "https://xmetr.gc/admin/districts/edit/1", "action_name": "district.edit", "controller_action": "Xmetr\\Location\\Http\\Controllers\\DistrictController@edit", "uri": "GET admin/districts/edit/{district}", "controller": "Xmetr\\Location\\Http\\Controllers\\DistrictController@edit<a href=\"phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Flocation%2Fsrc%2FHttp%2FControllers%2FDistrictController.php&line=51\" class=\"phpdebugbar-widgets-editor-link\"></a>", "namespace": "Xmetr\\Location\\Http\\Controllers", "prefix": "admin/districts", "file": "<a href=\"phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fplugins%2Flocation%2Fsrc%2FHttp%2FControllers%2FDistrictController.php&line=51\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">platform/plugins/location/src/Http/Controllers/DistrictController.php:51-56</a>", "middleware": "web, core, auth", "duration": "1.56s", "peak_memory": "60MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-301044755 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-301044755\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-554628936 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-554628936\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1009092435 data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">xmetr.gc</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>dnt</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">https://xmetr.gc/admin/districts</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"50 characters\">en-US,en;q=0.9,ur;q=0.8,pl;q=0.7,ru;q=0.6,ar;q=0.5</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1687 characters\">remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IjE5eVFoNXI3S3dCZTVpWVUvb1ZlTkE9PSIsInZhbHVlIjoiVFFEOHg1MHRNUCtIQndDL1FUdThDSVJxSzFQTTlRRWNjUGJtalZRTmpvRFQrQ1UybGVrWnR6dUN0ZU16VVJXRWg0UVg5cFdaQmlmL212RUdPSjJLcUgray8wa3JyeFU3LzUrRUs3by9jVXM1cCt0MDZCTXZjczI2RTRNR0xqeTBJT1dpcWNNQTdEeW8wYkZ1S3lUc1pZSVFCd2FiN2EvdTRmZGdiU2FOYVREVUtPdGw5SG1xOUZHVU5Db0Zwa1lvVTRXRDFPOXBhNnZOQ3hkWjJjU2JtaDRQRU9WYXRwczhPTmM1Wit2aThBcz0iLCJtYWMiOiI0NThmZWI3NDI2NThmMTIxOWRlN2JiMjljY2Y3MjQzMDUzNmQyYjA5NzllZTg4YmY2ZTc5MGExYjEzODJhMGU1IiwidGFnIjoiIn0%3D; _ga=GA1.1.2064844405.1743885089; cookie_for_consent=1; wishlist=657; _hjSessionUser_6417422=eyJpZCI6IjhkMzA2ODc1LTcwNjctNTU3Yi1hNDljLTdhNmEyNTdkM2QxOCIsImNyZWF0ZWQiOjE3NDgzODAwMDM5NTcsImV4aXN0aW5nIjp0cnVlfQ==; _hjSession_6417422=eyJpZCI6ImE5NTU4ZTg1LTkzYjItNDg3Ni04OWZiLWRiNGE2NTQyZmJlMyIsImMiOjE3NDk5MzU4MzMwNDQsInMiOjEsInIiOjAsInNiIjowLCJzciI6MCwic2UiOjAsImZzIjowfQ==; _ga_KQ6X76DET4=GS2.1.s1749935834$o64$g1$t1749935851$j43$l0$h0; XSRF-TOKEN=eyJpdiI6IlNCZ0RjV2FEM2QxdFNZZXRoNE4rQnc9PSIsInZhbHVlIjoiTURtSitEUWc4QVVuMkwvVm90WGQ1eEpxUnA2MXg0cEcrdXEvVkZROG9DMUFvRmkzZGdaM2Fxd2pqaGF3NVBvZXErVE53UjE3QitMUDhsRlgrdEcreHl4NjRDN0E1ZENEQjJsakVzMkU2VW91enVLaU1NMG84WlhZNnJCV0FiVDIiLCJtYWMiOiJiMGZjZjZiNzUwZDYyN2ExY2MxZjVkMjk2NmU4YWE5YWI4NTgxYmU2NDEwYTM0YTExMWQ0YmYzODljNTQ3MGYwIiwidGFnIjoiIn0%3D; xmetr_session=eyJpdiI6InBDYlZ1VGRXUHhpL3l1QXpjNXpOVHc9PSIsInZhbHVlIjoiOUZtS2YzbmxrQjE2aG5LcnpOcncxdEVjV01NZVJPMU43RjJiMHhsR1l6L2Qrb1o1aFdhU0hJTmM3QzBzd2tJWE5wclNjWlZKRnhGTHNEZ2d1Q000S3B2NjBSbjBYVEs0UUVyZmxlY2pCSmd5VC8zKzUvbldNMzl5ZnhPWGFmdlQiLCJtYWMiOiIwNmNiNmYwYjYxMzA4M2FkYWNlN2YzODFmOWE4MzAzNTM0YTY5ODBlYTBkNjY2YTFiMGY2NTIyYTgwZjA0NWZiIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1009092435\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1681347901 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"123 characters\">1|1uQwj4bI0uiYsJi0APplQif6Zji5Zq40vBOmafc1oj6NYK3vu89uqM3GLC7O|$2y$04$Astns3SXblBceX03zRicBuVKoyIRRFaOY1GuvREgnRYhHEelfNbdu</span>\"\n  \"<span class=sf-dump-key>_ga</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_for_consent</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>wishlist</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_hjSessionUser_6417422</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_hjSession_6417422</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_ga_KQ6X76DET4</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Rjk5dM1E37zSzatTdbPstutuCWngDisDc2aBepFu</span>\"\n  \"<span class=sf-dump-key>xmetr_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">lgniq8MYOTQ2XArI9qSaENnked1vRTrQhDkCFngG</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1681347901\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-321826401 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 14 Jun 2025 21:31:18 GMT</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-321826401\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-859190120 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Rjk5dM1E37zSzatTdbPstutuCWngDisDc2aBepFu</span>\"\n  \"<span class=sf-dump-key>language</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>new</span>\" => []\n    \"<span class=sf-dump-key>old</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"32 characters\">https://xmetr.gc/admin/districts</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-859190120\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "https://xmetr.gc/admin/districts/edit/1", "action_name": "district.edit", "controller_action": "Xmetr\\Location\\Http\\Controllers\\DistrictController@edit"}, "badge": null}}