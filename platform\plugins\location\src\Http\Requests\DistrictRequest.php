<?php

namespace Xmetr\Location\Http\Requests;

use Xmetr\Base\Enums\BaseStatusEnum;
use Xmetr\Base\Rules\MediaImageRule;
use Xmetr\Base\Rules\OnOffRule;
use Xmetr\Support\Http\Requests\Request;
use Illuminate\Validation\Rule;

class DistrictRequest extends Request
{
    public function rules(): array
    {
        return [
            'name' => ['required', 'string', 'max:250'],
            'city_id' => ['required', 'exists:cities,id'],
            'state_id' => ['nullable', 'exists:states,id'],
            'country_id' => ['nullable', 'exists:countries,id'],
            'slug' => [
                'nullable',
                'string',
                Rule::unique('districts', 'slug')->ignore($this->route('district')),
            ],
            'image' => ['nullable', 'string', new MediaImageRule()],
            'order' => ['required', 'integer', 'min:0', 'max:127'],
            'status' => [Rule::in(BaseStatusEnum::values())],
            'is_default' => [new OnOffRule()],
            'district_description' => ['nullable', 'string'],
            'amenities_rating' => ['nullable', 'numeric', 'min:0', 'max:5'],
            'amenities_comment' => ['nullable', 'string'],
            'transport_rating' => ['nullable', 'numeric', 'min:0', 'max:5'],
            'transport_comment' => ['nullable', 'string'],
            'safety_rating' => ['nullable', 'numeric', 'min:0', 'max:5'],
            'safety_comment' => ['nullable', 'string'],
            'green_spaces_rating' => ['nullable', 'numeric', 'min:0', 'max:5'],
            'green_spaces_comment' => ['nullable', 'string'],
            'noise_rating' => ['nullable', 'numeric', 'min:0', 'max:5'],
            'noise_comment' => ['nullable', 'string'],
            'rent_rating' => ['nullable', 'numeric', 'min:0', 'max:5'],
            'rent_comment' => ['nullable', 'string'],
            'atmosphere_rating' => ['nullable', 'numeric', 'min:0', 'max:5'],
            'atmosphere_comment' => ['nullable', 'string'],
        ];
    }
}
