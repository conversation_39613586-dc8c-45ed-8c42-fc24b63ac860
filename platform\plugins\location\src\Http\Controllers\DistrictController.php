<?php

namespace Xmetr\Location\Http\Controllers;

use Xmetr\Base\Facades\BaseHelper;
use Xmetr\Base\Http\Actions\DeleteResourceAction;
use Xmetr\Base\Http\Controllers\BaseController;
use Xmetr\Base\Supports\Breadcrumb;
use Xmetr\Location\Forms\DistrictForm;
use Xmetr\Location\Http\Requests\DistrictRequest;
use Xmetr\Location\Http\Resources\DistrictResource;
use Xmetr\Location\Models\District;
use Xmetr\Location\Tables\DistrictTable;
use Illuminate\Http\Request;

class DistrictController extends BaseController
{
    protected function breadcrumb(): Breadcrumb
    {
        return parent::breadcrumb()
            ->add(trans('plugins/location::location.name'))
            ->add(trans('plugins/location::district.name'), route('district.index'));
    }

    public function index(DistrictTable $table)
    {
        $this->pageTitle(trans('plugins/location::district.name'));

        return $table->renderTable();
    }

    public function create()
    {
        $this->pageTitle(trans('plugins/location::district.create'));

        return DistrictForm::create()->renderForm();
    }

    public function store(DistrictRequest $request)
    {
        $form = DistrictForm::create()->setRequest($request);
        $form->save();

        return $this
            ->httpResponse()
            ->setPreviousRoute('district.index')
            ->setNextRoute('district.edit', $form->getModel()->getKey())
            ->withCreatedSuccessMessage();
    }

    public function edit(District $district)
    {
        $this->pageTitle(trans('core/base::forms.edit_item', ['name' => $district->name]));

        return DistrictForm::createFromModel($district)->renderForm();
    }

    public function update(District $district, DistrictRequest $request)
    {
        DistrictForm::createFromModel($district)->setRequest($request)->save();

        return $this
            ->httpResponse()
            ->setPreviousRoute('district.index')
            ->withUpdatedSuccessMessage();
    }

    public function destroy(District $district)
    {
        return DeleteResourceAction::make($district);
    }

    public function getList(Request $request)
    {
        $keyword = BaseHelper::stringify($request->input('q'));

        if (! $keyword) {
            return $this
                ->httpResponse()
                ->setData([]);
        }

        $data = District::query()
            ->where('name', 'LIKE', '%' . $keyword . '%')
            ->select(['id', 'name'])
            ->take(10)
            ->oldest('order')
            ->oldest('name')
            ->get();

        $data->prepend(new District(['id' => 0, 'name' => trans('plugins/location::district.select_district')]));

        return $this
            ->httpResponse()
            ->setData(DistrictResource::collection($data));
    }

    public function ajaxGetDistricts(Request $request)
    {
        $data = District::query()
            ->with('slugable', 'country', 'state', 'city')
            ->wherePublished()
            ->orderBy('order')
            ->orderBy('name');

        $cityId = $request->input('city_id');

        if ($cityId && $cityId != 'null') {
            $data = $data->where('city_id', $cityId);
        }

        $stateId = $request->input('state_id');

        if ($stateId && $stateId != 'null') {
            $data = $data->where('state_id', $stateId);
        }

        $countryId = $request->input('country_id');

        if ($countryId && $countryId != 'null') {
            $data = $data->where('country_id', $countryId);
        }

        $keyword = BaseHelper::stringify($request->query('k'));

        if ($keyword) {
            $data = $data
                ->where('name', 'LIKE', '%' . $keyword . '%')
                ->paginate(10);
        } else {
            $data = $data->get();
        }

        if ($keyword) {
            return $this
                ->httpResponse()
                ->setData([DistrictResource::collection($data), 'total' => $data->total()]);
        }

        $data->prepend(new District(['id' => 0, 'name' => trans('plugins/location::district.select_district')]));

        return $this
            ->httpResponse()
            ->setData(DistrictResource::collection($data));
    }
}
