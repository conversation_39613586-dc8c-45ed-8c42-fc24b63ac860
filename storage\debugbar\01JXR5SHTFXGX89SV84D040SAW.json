{"__meta": {"id": "01JXR5SHTFXGX89SV84D040SAW", "datetime": "2025-06-14 21:33:38", "utime": **********.000156, "method": "POST", "uri": "/ajax/slug/create", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.140025, "end": **********.00017, "duration": 0.860145092010498, "duration_str": "860ms", "measures": [{"label": "Booting", "start": **********.140025, "relative_start": 0, "end": **********.936814, "relative_end": **********.936814, "duration": 0.****************, "duration_str": "797ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.936828, "relative_start": 0.****************, "end": **********.000172, "relative_end": 1.9073486328125e-06, "duration": 0.*****************, "duration_str": "63.34ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.957054, "relative_start": 0.****************, "end": **********.973784, "relative_end": **********.973784, "duration": 0.016730070114135742, "duration_str": "16.73ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.997365, "relative_start": 0.****************, "end": **********.998082, "relative_end": **********.998082, "duration": 0.0007169246673583984, "duration_str": "717μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "10.x", "tooltip": {"Laravel Version": "10.48.29", "PHP Version": "8.3.17", "Environment": "local", "Debug Mode": "Enabled", "URL": "xmetr.gc", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "queries": {"count": 1, "nb_statements": 1, "nb_visible_statements": 1, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.00195, "accumulated_duration_str": "1.95ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select exists(select * from `slugs` where (`key` = 'city-1' and `prefix` = 'rent-properties') and `id` != '0') as `exists`", "type": "query", "params": [], "bindings": ["city-1", "rent-properties", "0"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/packages/slug/src/Services/SlugService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\slug\\src\\Services\\SlugService.php", "line": 42}, {"index": 12, "namespace": null, "name": "platform/packages/slug/src/Services/SlugService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\slug\\src\\Services\\SlugService.php", "line": 23}, {"index": 13, "namespace": null, "name": "platform/packages/slug/src/Http/Controllers/SlugController.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\slug\\src\\Http\\Controllers\\SlugController.php", "line": 20}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\xmetr\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.993811, "duration": 0.00195, "duration_str": "1.95ms", "memory": 0, "memory_str": null, "filename": "SlugService.php:42", "source": {"index": 11, "namespace": null, "name": "platform/packages/slug/src/Services/SlugService.php", "file": "D:\\laragon\\www\\xmetr\\platform\\packages\\slug\\src\\Services\\SlugService.php", "line": 42}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fpackages%2Fslug%2Fsrc%2FServices%2FSlugService.php&line=42", "ajax": false, "filename": "SlugService.php", "line": "42"}, "connection": "xmetr", "explain": null, "start_percent": 0, "width_percent": 100}]}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "https://xmetr.gc/ajax/slug/create", "action_name": "slug.create", "controller_action": "Xmetr\\Slug\\Http\\Controllers\\SlugController@store", "uri": "POST ajax/slug/create", "controller": "Xmetr\\Slug\\Http\\Controllers\\SlugController@store<a href=\"phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fpackages%2Fslug%2Fsrc%2FHttp%2FControllers%2FSlugController.php&line=18\" class=\"phpdebugbar-widgets-editor-link\"></a>", "namespace": "Xmetr\\Slug\\Http\\Controllers", "prefix": "/ajax/slug", "file": "<a href=\"phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fxmetr%2Fplatform%2Fpackages%2Fslug%2Fsrc%2FHttp%2FControllers%2FSlugController.php&line=18\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">platform/packages/slug/src/Http/Controllers/SlugController.php:18-25</a>", "middleware": "web, core", "duration": "857ms", "peak_memory": "44MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-211178616 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-211178616\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-880341242 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"6 characters\">City 1</span>\"\n  \"<span class=sf-dump-key>slug_id</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>model</span>\" => \"<span class=sf-dump-str title=\"26 characters\">Xmetr\\Location\\Models\\City</span>\"\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Rjk5dM1E37zSzatTdbPstutuCWngDisDc2aBepFu</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-880341242\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1122346312 data-indent-pad=\"  \"><span class=sf-dump-note>array:20</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">xmetr.gc</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">124</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-xsrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6ImFhL2phUzJLVmVSN1ROWVJkWStWK2c9PSIsInZhbHVlIjoieE1zU0p6cFU2ZURuUEUxRFNJY2M3MmlKckdkbEtXdVRXSW5haFFvYmdIVDQwT0dWMll2T3JXWjd4clptS0d0S3llbDZ1ZDhxa1ZGR2dvWGRjaC9tU09RdGEvRUthTi9nbUMxTHNrRm1nQi9qYjFaMlZKMjNGbE1PTWNJY0dDOHIiLCJtYWMiOiI1NGI3NzdlNzUyOGQyNTRkY2UyOTBmODVlNTY2YmJiODljMGQ5OTdmNTQ2ZTVlN2IxZWQzNmIwZTljYjdmYTRkIiwidGFnIjoiIn0=</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/json, text/plain, */*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>dnt</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">https://xmetr.gc</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"36 characters\">https://xmetr.gc/admin/cities/create</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"50 characters\">en-US,en;q=0.9,ur;q=0.8,pl;q=0.7,ru;q=0.6,ar;q=0.5</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1687 characters\">remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IjE5eVFoNXI3S3dCZTVpWVUvb1ZlTkE9PSIsInZhbHVlIjoiVFFEOHg1MHRNUCtIQndDL1FUdThDSVJxSzFQTTlRRWNjUGJtalZRTmpvRFQrQ1UybGVrWnR6dUN0ZU16VVJXRWg0UVg5cFdaQmlmL212RUdPSjJLcUgray8wa3JyeFU3LzUrRUs3by9jVXM1cCt0MDZCTXZjczI2RTRNR0xqeTBJT1dpcWNNQTdEeW8wYkZ1S3lUc1pZSVFCd2FiN2EvdTRmZGdiU2FOYVREVUtPdGw5SG1xOUZHVU5Db0Zwa1lvVTRXRDFPOXBhNnZOQ3hkWjJjU2JtaDRQRU9WYXRwczhPTmM1Wit2aThBcz0iLCJtYWMiOiI0NThmZWI3NDI2NThmMTIxOWRlN2JiMjljY2Y3MjQzMDUzNmQyYjA5NzllZTg4YmY2ZTc5MGExYjEzODJhMGU1IiwidGFnIjoiIn0%3D; _ga=GA1.1.2064844405.1743885089; cookie_for_consent=1; wishlist=657; _hjSessionUser_6417422=eyJpZCI6IjhkMzA2ODc1LTcwNjctNTU3Yi1hNDljLTdhNmEyNTdkM2QxOCIsImNyZWF0ZWQiOjE3NDgzODAwMDM5NTcsImV4aXN0aW5nIjp0cnVlfQ==; _hjSession_6417422=eyJpZCI6ImE5NTU4ZTg1LTkzYjItNDg3Ni04OWZiLWRiNGE2NTQyZmJlMyIsImMiOjE3NDk5MzU4MzMwNDQsInMiOjEsInIiOjAsInNiIjowLCJzciI6MCwic2UiOjAsImZzIjowfQ==; _ga_KQ6X76DET4=GS2.1.s1749935834$o64$g1$t1749935851$j43$l0$h0; XSRF-TOKEN=eyJpdiI6ImFhL2phUzJLVmVSN1ROWVJkWStWK2c9PSIsInZhbHVlIjoieE1zU0p6cFU2ZURuUEUxRFNJY2M3MmlKckdkbEtXdVRXSW5haFFvYmdIVDQwT0dWMll2T3JXWjd4clptS0d0S3llbDZ1ZDhxa1ZGR2dvWGRjaC9tU09RdGEvRUthTi9nbUMxTHNrRm1nQi9qYjFaMlZKMjNGbE1PTWNJY0dDOHIiLCJtYWMiOiI1NGI3NzdlNzUyOGQyNTRkY2UyOTBmODVlNTY2YmJiODljMGQ5OTdmNTQ2ZTVlN2IxZWQzNmIwZTljYjdmYTRkIiwidGFnIjoiIn0%3D; xmetr_session=eyJpdiI6IkJZTnNkWkxPWVVBem8yZWQyclBFVGc9PSIsInZhbHVlIjoieGkzbzdtUEZiUkwweU1hK0VJK3J6U0ZVWW1XOVhkRis2cDI3eHF4Q1ZwQm1oeXBUOE1HanBrUzJSSEwweldwS0gyNURLcnpPSGJsZVdtTjFmYTlJSVdySDZzVGR2dFVuOUZGclRGUFIrWmJ4RlF2TUlWWTExN282VmxKM1ljZ2QiLCJtYWMiOiI2OTVkODc0YTg0ZWM0OTEzZTYwMmNmYzEwNjNkYzA4YjM1OTU4OWNkNzI4YzE4ZjJmYzZiZjRmNzc1NDgyOTU5IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1122346312\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-836402629 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"123 characters\">1|1uQwj4bI0uiYsJi0APplQif6Zji5Zq40vBOmafc1oj6NYK3vu89uqM3GLC7O|$2y$04$Astns3SXblBceX03zRicBuVKoyIRRFaOY1GuvREgnRYhHEelfNbdu</span>\"\n  \"<span class=sf-dump-key>_ga</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_for_consent</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>wishlist</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_hjSessionUser_6417422</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_hjSession_6417422</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_ga_KQ6X76DET4</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Rjk5dM1E37zSzatTdbPstutuCWngDisDc2aBepFu</span>\"\n  \"<span class=sf-dump-key>xmetr_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">lgniq8MYOTQ2XArI9qSaENnked1vRTrQhDkCFngG</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-836402629\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1923835118 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 14 Jun 2025 21:33:37 GMT</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1923835118\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1591512209 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Rjk5dM1E37zSzatTdbPstutuCWngDisDc2aBepFu</span>\"\n  \"<span class=sf-dump-key>language</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>new</span>\" => []\n    \"<span class=sf-dump-key>old</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"36 characters\">https://xmetr.gc/admin/cities/create</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1591512209\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "https://xmetr.gc/ajax/slug/create", "action_name": "slug.create", "controller_action": "Xmetr\\Slug\\Http\\Controllers\\SlugController@store"}, "badge": null}}