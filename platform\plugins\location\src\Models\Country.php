<?php

namespace Xmetr\Location\Models;

use Xmetr\Base\Casts\SafeContent;
use Xmetr\Base\Enums\BaseStatusEnum;
use Xmetr\Base\Models\BaseModel;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Xmetr\RealEstate\Models\Property;

class Country extends BaseModel
{
    protected $table = 'countries';

    protected $fillable = [
        'name',
        'nationality',
        'code',
        'order',
        'is_default',
        'status',
        'image',
    ];

    protected $casts = [
        'status' => BaseStatusEnum::class,
        'name' => SafeContent::class,
        'nationality' => SafeContent::class,
        'code' => SafeContent::class,
        'is_default' => 'bool',
        'order' => 'int',
    ];

    protected static function booted(): void
    {
        static::deleted(function (Country $country): void {
            $country->districts()->delete();
            $country->states()->delete();
            $country->cities()->delete();
        });
    }

    public function states(): HasMany
    {
        return $this->hasMany(State::class);
    }

    public function cities(): HasMany
    {
        return $this->hasMany(City::class);
    }

    public function districts(): HasMany
    {
        return $this->hasMany(District::class);
    }

    public function properties()
    {
        return $this->hasMany(Property::class, 'country_id', 'id');
    }
}
