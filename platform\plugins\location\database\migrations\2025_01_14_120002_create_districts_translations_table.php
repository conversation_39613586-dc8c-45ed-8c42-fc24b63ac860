<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class () extends Migration {
    public function up(): void
    {
        if (! Schema::hasTable('districts_translations')) {
            Schema::create('districts_translations', function (Blueprint $table): void {
                $table->string('lang_code', 20);
                $table->foreignId('districts_id');
                $table->string('name', 120)->nullable();
                $table->string('slug', 120)->nullable();
                $table->text('district_description')->nullable();
                $table->text('amenities_comment')->nullable();
                $table->text('transport_comment')->nullable();
                $table->text('safety_comment')->nullable();
                $table->text('green_spaces_comment')->nullable();
                $table->text('noise_comment')->nullable();
                $table->text('rent_comment')->nullable();
                $table->text('atmosphere_comment')->nullable();

                $table->primary(['lang_code', 'districts_id'], 'districts_translations_primary');
                $table->index(['districts_id'], 'districts_translations_districts_id_index');
            });
        }
    }

    public function down(): void
    {
        Schema::dropIfExists('districts_translations');
    }
};
