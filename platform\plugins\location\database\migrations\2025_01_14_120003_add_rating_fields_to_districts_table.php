<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class () extends Migration {
    public function up(): void
    {
        if (Schema::hasTable('districts')) {
            Schema::table('districts', function (Blueprint $table): void {
                $table->text('district_description')->nullable()->after('name');
                $table->float('amenities_rating', 2, 1)->nullable()->after('district_description');
                $table->text('amenities_comment')->nullable()->after('amenities_rating');
                $table->float('transport_rating', 2, 1)->nullable()->after('amenities_comment');
                $table->text('transport_comment')->nullable()->after('transport_rating');
                $table->float('safety_rating', 2, 1)->nullable()->after('transport_comment');
                $table->text('safety_comment')->nullable()->after('safety_rating');
                $table->float('green_spaces_rating', 2, 1)->nullable()->after('safety_comment');
                $table->text('green_spaces_comment')->nullable()->after('green_spaces_rating');
                $table->float('noise_rating', 2, 1)->nullable()->after('green_spaces_comment');
                $table->text('noise_comment')->nullable()->after('noise_rating');
                $table->float('rent_rating', 2, 1)->nullable()->after('noise_comment');
                $table->text('rent_comment')->nullable()->after('rent_rating');
                $table->float('atmosphere_rating', 2, 1)->nullable()->after('rent_rating');
                $table->text('atmosphere_comment')->nullable()->after('atmosphere_rating');
            });
        }
    }

    public function down(): void
    {
        if (Schema::hasTable('districts')) {
            Schema::table('districts', function (Blueprint $table): void {
                $table->dropColumn([
                    'district_description',
                    'amenities_rating',
                    'amenities_comment',
                    'transport_rating',
                    'transport_comment',
                    'safety_rating',
                    'safety_comment',
                    'green_spaces_rating',
                    'green_spaces_comment',
                    'noise_rating',
                    'noise_comment',
                    'rent_rating',
                    'rent_comment',
                    'atmosphere_rating',
                    'atmosphere_comment',
                ]);
            });
        }
    }
};
