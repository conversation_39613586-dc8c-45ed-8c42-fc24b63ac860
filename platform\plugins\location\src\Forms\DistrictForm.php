<?php

namespace Xmetr\Location\Forms;

use Xmetr\Base\Facades\Assets;
use Xmetr\Base\Forms\FieldOptions\IsDefaultFieldOption;
use Xmetr\Base\Forms\FieldOptions\MediaImageFieldOption;
use Xmetr\Base\Forms\FieldOptions\NameFieldOption;
use Xmetr\Base\Forms\FieldOptions\SortOrderFieldOption;
use Xmetr\Base\Forms\FieldOptions\StatusFieldOption;
use Xmetr\Base\Forms\Fields\MediaImageField;
use Xmetr\Base\Forms\Fields\NumberField;
use Xmetr\Base\Forms\Fields\OnOffField;
use Xmetr\Base\Forms\Fields\SelectField;
use Xmetr\Base\Forms\Fields\TextField;
use Xmetr\Base\Forms\FormAbstract;
use Xmetr\Location\Http\Requests\DistrictRequest;
use Xmetr\Location\Models\City;
use Xmetr\Location\Models\Country;
use Xmetr\Location\Models\District;
use Xmetr\Location\Models\State;

class DistrictForm extends FormAbstract
{
    public function setup(): void
    {
        Assets::addScriptsDirectly('vendor/core/plugins/location/js/location.js');
        $countries = Country::query()
            ->wherePublished()
            ->orderBy('order')
            ->oldest('name')
            ->pluck('name', 'id')
            ->all();

        $states = [];
        $cities = [];

        if ($this->getModel() && $this->getModel()->country_id) {
            $states = State::query()
                ->where('country_id', $this->getModel()->country_id)
                ->wherePublished()
                ->orderBy('order')
                ->oldest('name')
                ->pluck('name', 'id')
                ->all();

            if ($this->getModel()->state_id) {
                $cities = City::query()
                    ->where('state_id', $this->getModel()->state_id)
                    ->wherePublished()
                    ->orderBy('order')
                    ->oldest('name')
                    ->pluck('name', 'id')
                    ->all();
            }
        }

        $this
            ->model(District::class)
            ->setValidatorClass(DistrictRequest::class)
            ->add('name', TextField::class, NameFieldOption::make()->required())
            ->add('country_id', SelectField::class, [
                'label' => trans('plugins/location::district.country'),
                'required' => true,
                'attr' => [
                    'id' => 'country_id',
                    'class' => 'select-search-full',
                    'data-type' => 'country',
                ],
                'choices' => [0 => trans('plugins/location::district.select_country')] + $countries,
            ])
            ->add('state_id', SelectField::class, [
                'label' => trans('plugins/location::district.state'),
                'attr' => [
                    'id' => 'state_id',
                    'data-url' => route('ajax.states-by-country'),
                    'class' => 'select-search-full',
                    'data-type' => 'state',
                ],
                'choices' => [0 => trans('plugins/location::district.select_state')] + $states,
            ])
            ->add('city_id', SelectField::class, [
                'label' => trans('plugins/location::district.city'),
                'attr' => [
                    'id' => 'city_id',
                    'data-url' => route('ajax.cities-by-state'),
                    'class' => 'select-search-full',
                    'data-type' => 'city',
                ],
                'choices' => [0 => trans('plugins/location::district.select_city')] + $cities,
            ])
            ->add('order', NumberField::class, SortOrderFieldOption::make())
            ->add('is_default', OnOffField::class, IsDefaultFieldOption::make())
            ->add('status', SelectField::class, StatusFieldOption::make())
            ->add('image', MediaImageField::class, MediaImageFieldOption::make())
            ->setBreakFieldPoint('status');
    }
}
