<?php

require_once 'bootstrap/app.php';

$app = \Illuminate\Foundation\Application::getInstance();
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

use Xmetr\Location\Models\{Country, State, City, District};

try {
    // Create test country if not exists
    $country = Country::firstOrCreate([
        'name' => 'Pakistan'
    ], [
        'status' => 'published',
        'order' => 0,
        'is_default' => 0
    ]);
    
    echo "Country: {$country->name} (ID: {$country->id})\n";
    
    // Create test state if not exists
    $state = State::firstOrCreate([
        'name' => 'Punjab',
        'country_id' => $country->id
    ], [
        'status' => 'published',
        'order' => 0,
        'is_default' => 0
    ]);
    
    echo "State: {$state->name} (ID: {$state->id})\n";
    
    // Create test city if not exists
    $city = City::firstOrCreate([
        'name' => 'Lahore',
        'state_id' => $state->id,
        'country_id' => $country->id
    ], [
        'status' => 'published',
        'order' => 0,
        'is_default' => 0
    ]);
    
    echo "City: {$city->name} (ID: {$city->id})\n";
    
    // Create test districts
    $districts = ['DHA', 'Gulberg', 'Model Town', 'Johar Town'];
    
    foreach ($districts as $districtName) {
        $district = District::firstOrCreate([
            'name' => $districtName,
            'city_id' => $city->id,
            'state_id' => $state->id,
            'country_id' => $country->id
        ], [
            'status' => 'published',
            'order' => 0,
            'is_default' => 0
        ]);
        
        echo "District: {$district->name} (ID: {$district->id})\n";
    }
    
    echo "\nTest data created successfully!\n";
    echo "Now test the AJAX endpoint: /ajax/districts-by-city?city_id={$city->id}\n";
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}
